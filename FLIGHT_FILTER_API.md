# Flight Filter API Documentation

## Overview
The Flight Filter API allows you to filter flight search results based on various criteria including airlines, price, duration, stops, cabin class, and more.

## Endpoints

### 1. Get Filtered Flight Offers
```
GET /api/flights/searches/{search_id}/offers
POST /api/flights/searches/{search_id}/offers/filter
```

Both endpoints support the same filter parameters. Use POST for complex filters or when URL length becomes an issue.

## Filter Parameters

### Airlines Filter
Filter by specific airlines using IATA codes:
```json
{
  "airlines": ["BA", "LH", "AF", "KL"]
}
```

### Exclude Airlines
Exclude specific airlines:
```json
{
  "exclude_airlines": ["FR", "U2"]
}
```

### Stops Filter
Filter by number of stops:
```json
{
  "stops": [0, 1]  // 0 = direct, 1 = one stop, etc.
}
```

### Direct Flights Only
Show only direct flights:
```json
{
  "direct_flights_only": true
}
```

### Price Range
Filter by price range (in the specified currency):
```json
{
  "min_price": 200,
  "max_price": 1000,
  "currency": "USD"
}
```

### Cabin Class
Filter by cabin class:
```json
{
  "cabin_classes": ["economy", "premium_economy", "business", "first"]
}
```

### Duration Filter
Filter by flight duration (in minutes):
```json
{
  "min_duration": 120,    // 2 hours
  "max_duration": 600     // 10 hours
}
```

### Departure Time
Filter by departure time ranges:
```json
{
  "departure_time_ranges": [
    {"start": "06:00", "end": "12:00"},  // Morning flights
    {"start": "18:00", "end": "23:59"}   // Evening flights
  ]
}
```

### Arrival Time
Filter by arrival time ranges:
```json
{
  "arrival_time_ranges": [
    {"start": "09:00", "end": "17:00"}   // Business hours arrival
  ]
}
```

### Aircraft Type
Filter by aircraft type:
```json
{
  "aircraft": ["A320", "B737", "A350"]
}
```

### Ticket Conditions
Filter by refundable/changeable tickets:
```json
{
  "refundable": true,
  "changeable": true
}
```

### Baggage
Filter by included baggage:
```json
{
  "baggage_included": ["carry_on", "checked"]
}
```

### Sorting
Sort results by various criteria:
```json
{
  "sort_by": "price",        // "price", "duration", "departure_time", "arrival_time"
  "sort_order": "asc"        // "asc", "desc"
}
```

## Complete Example Request

```bash
curl -X POST http://localhost:8001/api/flights/searches/123/offers/filter \
  -H "Content-Type: application/json" \
  -d '{
    "airlines": ["BA", "LH"],
    "stops": [0, 1],
    "max_price": 800,
    "currency": "USD",
    "cabin_classes": ["economy", "premium_economy"],
    "departure_time_ranges": [
      {"start": "06:00", "end": "12:00"}
    ],
    "direct_flights_only": false,
    "refundable": true,
    "sort_by": "price",
    "sort_order": "asc",
    "page": 1,
    "per_page": 20
  }'
```

## Response Format

```json
{
  "success": true,
  "data": {
    "offers": [
      {
        "id": "off_123456",
        "total_amount": "252.11",
        "total_currency": "EUR",
        "owner": {
          "iata_code": "BA",
          "name": "British Airways"
        },
        "slices": [...],
        "passengers": [...],
        "conditions": {...}
      }
    ],
    "filter_options": {
      "airlines": [
        {"code": "BA", "name": "British Airways"},
        {"code": "LH", "name": "Lufthansa"}
      ],
      "stops": [0, 1, 2],
      "cabin_classes": ["economy", "premium_economy", "business"],
      "aircraft": ["A320", "B737", "A350"],
      "price_range": {"min": 150.00, "max": 1200.00},
      "duration_range": {"min": 120, "max": 840}
    },
    "applied_filters": {
      "airlines": ["BA", "LH"],
      "max_price": 800
    }
  },
  "meta": {
    "pagination": {
      "current_page": 1,
      "last_page": 5,
      "per_page": 20,
      "total": 95,
      "from": 1,
      "to": 20
    },
    "search_id": "123",
    "original_total": 150,
    "filtered_total": 95,
    "filtered": true
  }
}
```

## Filter Options Response
The `filter_options` object contains all available filter values extracted from the search results:

- **airlines**: List of all airlines in results with their codes and names
- **stops**: Array of available stop counts
- **cabin_classes**: Available cabin classes
- **aircraft**: Available aircraft types
- **price_range**: Min and max prices in the results
- **duration_range**: Min and max durations in minutes

## Error Responses

### Invalid Filter Parameters
```json
{
  "success": false,
  "message": "Invalid filter parameters",
  "errors": {
    "sort_by": "Invalid sort_by value. Must be one of: price, duration, departure_time, arrival_time",
    "price": "max_price must be greater than min_price"
  }
}
```

### Search Not Found
```json
{
  "success": false,
  "message": "Search not found",
  "error": "No query results for model [App\\Models\\FlightSearch] 999"
}
```

## Frontend Integration Tips

1. **Load Filter Options**: First call the endpoint without filters to get `filter_options`
2. **Build UI**: Use `filter_options` to build checkboxes, sliders, and dropdowns
3. **Apply Filters**: Send filter parameters to get filtered results
4. **Update UI**: Show applied filters and result counts

## Performance Notes

- Filtering is done in-memory for fast response times
- Maximum 100 results per page to prevent memory issues
- Filter options are cached per search for better performance
- Use pagination for large result sets
