{"info": {"name": "Travel Smart API", "description": "API collection for Travel Smart - Duffel Integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:8000/api", "type": "string"}], "item": [{"name": "Travel Service Management", "item": [{"name": "Get Available Providers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/travel/providers", "host": ["{{base_url}}"], "path": ["travel", "providers"]}}}, {"name": "Test Duffel Provider", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/travel/providers/duffel/test", "host": ["{{base_url}}"], "path": ["travel", "providers", "duffel", "test"]}}}, {"name": "Get Provider Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/travel/providers/duffel/stats", "host": ["{{base_url}}"], "path": ["travel", "providers", "duffel", "stats"]}}}, {"name": "Test All Providers", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/travel/providers/test-all", "host": ["{{base_url}}"], "path": ["travel", "providers", "test-all"]}}}]}, {"name": "Flight Operations", "item": [{"name": "Search Flights", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"passengers\": [\n    {\"type\": \"adult\"}\n  ],\n  \"slices\": [\n    {\n      \"origin\": \"LHR\",\n      \"destination\": \"JFK\",\n      \"departure_date\": \"2024-12-25\"\n    }\n  ],\n  \"cabin_class\": \"economy\"\n}"}, "url": {"raw": "{{base_url}}/flights/search", "host": ["{{base_url}}"], "path": ["flights", "search"]}}}, {"name": "Get Search Results", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/flights/searches/1", "host": ["{{base_url}}"], "path": ["flights", "searches", "1"]}}}, {"name": "Get All Searches", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/flights/searches?per_page=10", "host": ["{{base_url}}"], "path": ["flights", "searches"], "query": [{"key": "per_page", "value": "10"}]}}}, {"name": "Get Offer Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/flights/offers/1", "host": ["{{base_url}}"], "path": ["flights", "offers", "1"]}}}, {"name": "Get Search Offers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/flights/searches/1/offers?sort=total_amount&direction=asc", "host": ["{{base_url}}"], "path": ["flights", "searches", "1", "offers"], "query": [{"key": "sort", "value": "total_amount"}, {"key": "direction", "value": "asc"}]}}}, {"name": "Book Flight", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"offer_id\": 1,\n  \"passengers\": [\n    {\n      \"given_name\": \"<PERSON>\",\n      \"family_name\": \"<PERSON><PERSON>\"\n    }\n  ],\n  \"payments\": [\n    {\n      \"type\": \"balance\",\n      \"amount\": \"100.00\",\n      \"currency\": \"USD\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/flights/book", "host": ["{{base_url}}"], "path": ["flights", "book"]}}}, {"name": "Get Booking", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/flights/bookings/1", "host": ["{{base_url}}"], "path": ["flights", "bookings", "1"]}}}, {"name": "Get All Bookings", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/flights/bookings", "host": ["{{base_url}}"], "path": ["flights", "bookings"]}}}, {"name": "Cancel Booking", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/flights/bookings/1/cancel", "host": ["{{base_url}}"], "path": ["flights", "bookings", "1", "cancel"]}}}]}, {"name": "Reference Data", "item": [{"name": "Get Airports", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/flights/airports", "host": ["{{base_url}}"], "path": ["flights", "airports"]}}}, {"name": "Search Airports", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/flights/airports?query=London", "host": ["{{base_url}}"], "path": ["flights", "airports"], "query": [{"key": "query", "value": "London"}]}}}, {"name": "Get Airlines", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/flights/airlines", "host": ["{{base_url}}"], "path": ["flights", "airlines"]}}}]}]}