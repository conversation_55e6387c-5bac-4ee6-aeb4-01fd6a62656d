<?php

namespace Tests\Feature;

use App\Services\TravelServiceManager;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TravelServiceTest extends TestCase
{
    use RefreshDatabase;

    protected TravelServiceManager $travelManager;

    protected function setUp(): void
    {
        parent::setUp();
        $this->travelManager = app(TravelServiceManager::class);
    }

    /** @test */
    public function it_can_get_available_providers()
    {
        $response = $this->getJson('/api/travel/providers');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data',
                    'meta' => [
                        'total_providers',
                        'available_providers'
                    ]
                ]);
    }

    /** @test */
    public function it_can_test_duffel_provider()
    {
        $response = $this->getJson('/api/travel/providers/duffel/test');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'provider',
                        'available',
                        'connection_test'
                    ]
                ]);
    }

    /** @test */
    public function it_can_get_provider_stats()
    {
        $response = $this->getJson('/api/travel/providers/duffel/stats');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'provider',
                        'available'
                    ]
                ]);
    }

    /** @test */
    public function it_can_test_all_providers()
    {
        $response = $this->postJson('/api/travel/providers/test-all');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data',
                    'meta' => [
                        'total_providers',
                        'available_providers',
                        'connection_successful'
                    ]
                ]);
    }

    /** @test */
    public function it_validates_flight_search_request()
    {
        $response = $this->postJson('/api/flights/search', []);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['passengers', 'slices']);
    }

    /** @test */
    public function it_validates_passenger_data()
    {
        $response = $this->postJson('/api/flights/search', [
            'passengers' => [
                [] // Empty passenger
            ],
            'slices' => [
                [
                    'origin' => 'LHR',
                    'destination' => 'JFK',
                    'departure_date' => '2024-12-25'
                ]
            ]
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['passengers.0.type']);
    }

    /** @test */
    public function it_validates_slice_data()
    {
        $response = $this->postJson('/api/flights/search', [
            'passengers' => [
                ['type' => 'adult']
            ],
            'slices' => [
                [
                    'origin' => 'INVALID', // Invalid airport code
                    'destination' => 'JFK',
                    'departure_date' => '2024-12-25'
                ]
            ]
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['slices.0.origin']);
    }

    /** @test */
    public function it_can_get_airports()
    {
        $response = $this->getJson('/api/flights/airports');

        $response->assertStatus(200);
    }

    /** @test */
    public function it_can_search_airports_with_query()
    {
        $response = $this->getJson('/api/flights/airports?query=London');

        $response->assertStatus(200);
    }

    /** @test */
    public function it_can_get_airlines()
    {
        $response = $this->getJson('/api/flights/airlines');

        $response->assertStatus(200);
    }
}
