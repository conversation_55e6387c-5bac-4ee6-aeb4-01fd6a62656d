<?php

namespace App\DTOs;

use Carbon\Carbon;

/**
 * Data Transfer Object for flight search requests
 */
class FlightSearchRequest
{
    public function __construct(
        public readonly array $passengers,
        public readonly array $slices,
        public readonly ?string $cabinClass = null,
        public readonly ?int $maxConnections = null,
        public readonly ?array $privateFares = null,
        public readonly ?int $supplierTimeout = null
    ) {}

    /**
     * Create from array data
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        return new self(
            passengers: $data['passengers'] ?? [],
            slices: $data['slices'] ?? [],
            cabinClass: $data['cabin_class'] ?? null,
            maxConnections: $data['max_connections'] ?? null,
            privateFares: $data['private_fares'] ?? null,
            supplierTimeout: $data['supplier_timeout'] ?? null
        );
    }

    /**
     * Convert to array for API requests
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [
            'passengers' => $this->passengers,
            'slices' => $this->slices,
        ];

        if ($this->cabinClass) {
            $data['cabin_class'] = $this->cabinClass;
        }

        if ($this->maxConnections !== null) {
            $data['max_connections'] = $this->maxConnections;
        }

        if ($this->privateFares) {
            $data['private_fares'] = $this->privateFares;
        }

        return $data;
    }

    /**
     * Validate the request data
     *
     * @return array Array of validation errors, empty if valid
     */
    public function validate(): array
    {
        $errors = [];

        if (empty($this->passengers)) {
            $errors[] = 'At least one passenger is required';
        }

        if (empty($this->slices)) {
            $errors[] = 'At least one slice is required';
        }

        foreach ($this->passengers as $index => $passenger) {
            if (empty($passenger['type']) && empty($passenger['age'])) {
                $errors[] = "Passenger {$index}: Either type or age must be specified";
            }
        }

        foreach ($this->slices as $index => $slice) {
            if (empty($slice['origin'])) {
                $errors[] = "Slice {$index}: Origin is required";
            }
            if (empty($slice['destination'])) {
                $errors[] = "Slice {$index}: Destination is required";
            }
            if (empty($slice['departure_date'])) {
                $errors[] = "Slice {$index}: Departure date is required";
            }
        }

        return $errors;
    }
}
