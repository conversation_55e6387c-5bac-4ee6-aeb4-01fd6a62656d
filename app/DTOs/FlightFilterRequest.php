<?php

namespace App\DTOs;

class FlightFilterRequest
{
    public function __construct(
        public ?array $airlines = null,           // ['BA', 'LH', 'AF'] - IATA codes
        public ?array $stops = null,             // [0, 1, 2] - number of stops
        public ?array $cabinClasses = null,      // ['economy', 'premium_economy', 'business', 'first']
        public ?int $maxPrice = null,            // Maximum price in cents
        public ?int $minPrice = null,            // Minimum price in cents
        public ?string $currency = 'USD',        // Currency code
        public ?array $departureTimeRanges = null, // [['start' => '06:00', 'end' => '12:00']]
        public ?array $arrivalTimeRanges = null,   // [['start' => '18:00', 'end' => '23:59']]
        public ?int $maxDuration = null,         // Maximum duration in minutes
        public ?int $minDuration = null,         // Minimum duration in minutes
        public ?array $aircraft = null,          // ['A320', 'B737'] - aircraft types
        public ?bool $directFlightsOnly = null, // Only direct flights
        public ?array $excludeAirlines = null,  // Airlines to exclude
        public ?string $sortBy = 'price',       // 'price', 'duration', 'departure_time', 'arrival_time'
        public ?string $sortOrder = 'asc',      // 'asc', 'desc'
        public ?bool $refundable = null,        // Only refundable tickets
        public ?bool $changeable = null,        // Only changeable tickets
        public ?array $baggageIncluded = null,  // ['carry_on', 'checked']
    ) {}

    public function toArray(): array
    {
        return array_filter([
            'airlines' => $this->airlines,
            'stops' => $this->stops,
            'cabin_classes' => $this->cabinClasses,
            'max_price' => $this->maxPrice,
            'min_price' => $this->minPrice,
            'currency' => $this->currency,
            'departure_time_ranges' => $this->departureTimeRanges,
            'arrival_time_ranges' => $this->arrivalTimeRanges,
            'max_duration' => $this->maxDuration,
            'min_duration' => $this->minDuration,
            'aircraft' => $this->aircraft,
            'direct_flights_only' => $this->directFlightsOnly,
            'exclude_airlines' => $this->excludeAirlines,
            'sort_by' => $this->sortBy,
            'sort_order' => $this->sortOrder,
            'refundable' => $this->refundable,
            'changeable' => $this->changeable,
            'baggage_included' => $this->baggageIncluded,
        ], fn($value) => $value !== null);
    }

    public static function fromRequest(array $data): self
    {
        return new self(
            airlines: $data['airlines'] ?? null,
            stops: $data['stops'] ?? null,
            cabinClasses: $data['cabin_classes'] ?? null,
            maxPrice: isset($data['max_price']) ? (int) $data['max_price'] : null,
            minPrice: isset($data['min_price']) ? (int) $data['min_price'] : null,
            currency: $data['currency'] ?? 'USD',
            departureTimeRanges: $data['departure_time_ranges'] ?? null,
            arrivalTimeRanges: $data['arrival_time_ranges'] ?? null,
            maxDuration: isset($data['max_duration']) ? (int) $data['max_duration'] : null,
            minDuration: isset($data['min_duration']) ? (int) $data['min_duration'] : null,
            aircraft: $data['aircraft'] ?? null,
            directFlightsOnly: isset($data['direct_flights_only']) ? (bool) $data['direct_flights_only'] : null,
            excludeAirlines: $data['exclude_airlines'] ?? null,
            sortBy: $data['sort_by'] ?? 'price',
            sortOrder: $data['sort_order'] ?? 'asc',
            refundable: isset($data['refundable']) ? (bool) $data['refundable'] : null,
            changeable: isset($data['changeable']) ? (bool) $data['changeable'] : null,
            baggageIncluded: $data['baggage_included'] ?? null,
        );
    }

    public function validate(): array
    {
        $errors = [];

        if ($this->sortBy && !in_array($this->sortBy, ['price', 'duration', 'departure_time', 'arrival_time'])) {
            $errors['sort_by'] = 'Invalid sort_by value. Must be one of: price, duration, departure_time, arrival_time';
        }

        if ($this->sortOrder && !in_array($this->sortOrder, ['asc', 'desc'])) {
            $errors['sort_order'] = 'Invalid sort_order value. Must be asc or desc';
        }

        if ($this->maxPrice && $this->minPrice && $this->maxPrice < $this->minPrice) {
            $errors['price'] = 'max_price must be greater than min_price';
        }

        if ($this->maxDuration && $this->minDuration && $this->maxDuration < $this->minDuration) {
            $errors['duration'] = 'max_duration must be greater than min_duration';
        }

        return $errors;
    }
}
