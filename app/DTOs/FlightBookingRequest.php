<?php

namespace App\DTOs;

/**
 * Data Transfer Object for flight booking requests
 */
class FlightBookingRequest
{
    public function __construct(
        public readonly string $offerId,
        public readonly array $passengers,
        public readonly array $payments,
        public readonly ?array $services = null,
        public readonly ?string $type = 'instant'
    ) {}

    /**
     * Create from array data
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        return new self(
            offerId: $data['offer_id'],
            passengers: $data['passengers'],
            payments: $data['payments'],
            services: $data['services'] ?? null,
            type: $data['type'] ?? 'instant'
        );
    }

    /**
     * Convert to array for API requests
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [
            'selected_offers' => [$this->offerId],
            'passengers' => $this->passengers,
            'payments' => $this->payments,
            'type' => $this->type,
        ];

        if ($this->services) {
            $data['services'] = $this->services;
        }

        return $data;
    }

    /**
     * Validate the request data
     *
     * @return array Array of validation errors, empty if valid
     */
    public function validate(): array
    {
        $errors = [];

        if (empty($this->offerId)) {
            $errors[] = 'Offer ID is required';
        }

        if (empty($this->passengers)) {
            $errors[] = 'At least one passenger is required';
        }

        if (empty($this->payments)) {
            $errors[] = 'Payment information is required';
        }

        foreach ($this->passengers as $index => $passenger) {
            if (empty($passenger['given_name'])) {
                $errors[] = "Passenger {$index}: Given name is required";
            }
            if (empty($passenger['family_name'])) {
                $errors[] = "Passenger {$index}: Family name is required";
            }
        }

        return $errors;
    }
}
