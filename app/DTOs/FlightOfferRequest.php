<?php

namespace App\DTOs;

/**
 * Data Transfer Object for flight offer requests
 */
class FlightOfferRequest
{
    public function __construct(
        public readonly string $offerId,
        public readonly bool $returnAvailableServices = false
    ) {}

    /**
     * Create from array data
     *
     * @param array $data
     * @return self
     */
    public static function fromArray(array $data): self
    {
        return new self(
            offerId: $data['offer_id'],
            returnAvailableServices: $data['return_available_services'] ?? false
        );
    }

    /**
     * Convert to array for API requests
     *
     * @return array
     */
    public function toArray(): array
    {
        return [
            'offer_id' => $this->offerId,
            'return_available_services' => $this->returnAvailableServices,
        ];
    }

    /**
     * Validate the request data
     *
     * @return array Array of validation errors, empty if valid
     */
    public function validate(): array
    {
        $errors = [];

        if (empty($this->offerId)) {
            $errors[] = 'Offer ID is required';
        }

        return $errors;
    }
}
