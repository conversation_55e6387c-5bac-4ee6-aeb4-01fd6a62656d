<?php

namespace App\DTOs;

/**
 * Data Transfer Object for flight search responses
 */
class FlightSearchResponse
{
    public function __construct(
        public readonly bool $success,
        public readonly ?string $requestId = null,
        public readonly array $offers = [],
        public readonly ?string $message = null,
        public readonly ?array $errors = null,
        public readonly ?array $metadata = null
    ) {}

    /**
     * Create a successful response
     *
     * @param string $requestId
     * @param array $offers
     * @param array|null $metadata
     * @return self
     */
    public static function success(string $requestId, array $offers, ?array $metadata = null): self
    {
        return new self(
            success: true,
            requestId: $requestId,
            offers: $offers,
            metadata: $metadata
        );
    }

    /**
     * Create a failed response
     *
     * @param string $message
     * @param array|null $errors
     * @return self
     */
    public static function failed(string $message, ?array $errors = null): self
    {
        return new self(
            success: false,
            message: $message,
            errors: $errors
        );
    }

    /**
     * Convert to array
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [
            'success' => $this->success,
        ];

        if ($this->requestId) {
            $data['request_id'] = $this->requestId;
        }

        if ($this->success) {
            $data['offers'] = $this->offers;
            if ($this->metadata) {
                $data['metadata'] = $this->metadata;
            }
        } else {
            $data['message'] = $this->message;
            if ($this->errors) {
                $data['errors'] = $this->errors;
            }
        }

        return $data;
    }

    /**
     * Get the number of offers
     *
     * @return int
     */
    public function getOfferCount(): int
    {
        return count($this->offers);
    }

    /**
     * Check if response has offers
     *
     * @return bool
     */
    public function hasOffers(): bool
    {
        return $this->success && !empty($this->offers);
    }
}
