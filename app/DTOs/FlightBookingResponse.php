<?php

namespace App\DTOs;

/**
 * Data Transfer Object for flight booking responses
 */
class FlightBookingResponse
{
    public function __construct(
        public readonly bool $success,
        public readonly ?array $booking = null,
        public readonly ?string $bookingId = null,
        public readonly ?string $message = null,
        public readonly ?array $errors = null
    ) {}

    /**
     * Create a successful response
     *
     * @param array $booking
     * @param string $bookingId
     * @return self
     */
    public static function success(array $booking, string $bookingId): self
    {
        return new self(
            success: true,
            booking: $booking,
            bookingId: $bookingId
        );
    }

    /**
     * Create a failed response
     *
     * @param string $message
     * @param array|null $errors
     * @return self
     */
    public static function failed(string $message, ?array $errors = null): self
    {
        return new self(
            success: false,
            message: $message,
            errors: $errors
        );
    }

    /**
     * Convert to array
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [
            'success' => $this->success,
        ];

        if ($this->success) {
            $data['booking'] = $this->booking;
            $data['booking_id'] = $this->bookingId;
        } else {
            $data['message'] = $this->message;
            if ($this->errors) {
                $data['errors'] = $this->errors;
            }
        }

        return $data;
    }
}
