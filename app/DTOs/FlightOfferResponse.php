<?php

namespace App\DTOs;

/**
 * Data Transfer Object for flight offer responses
 */
class FlightOfferResponse
{
    public function __construct(
        public readonly bool $success,
        public readonly ?array $offer = null,
        public readonly ?string $message = null,
        public readonly ?array $errors = null
    ) {}

    /**
     * Create a successful response
     *
     * @param array $offer
     * @return self
     */
    public static function success(array $offer): self
    {
        return new self(
            success: true,
            offer: $offer
        );
    }

    /**
     * Create a failed response
     *
     * @param string $message
     * @param array|null $errors
     * @return self
     */
    public static function failed(string $message, ?array $errors = null): self
    {
        return new self(
            success: false,
            message: $message,
            errors: $errors
        );
    }

    /**
     * Convert to array
     *
     * @return array
     */
    public function toArray(): array
    {
        $data = [
            'success' => $this->success,
        ];

        if ($this->success) {
            $data['offer'] = $this->offer;
        } else {
            $data['message'] = $this->message;
            if ($this->errors) {
                $data['errors'] = $this->errors;
            }
        }

        return $data;
    }
}
