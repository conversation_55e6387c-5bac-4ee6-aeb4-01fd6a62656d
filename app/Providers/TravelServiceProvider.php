<?php

namespace App\Providers;

use App\Services\TravelServiceManager;
use Illuminate\Support\ServiceProvider;

class TravelServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(TravelServiceManager::class, function ($app) {
            return new TravelServiceManager($app);
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Publish configuration file
        $this->publishes([
            __DIR__.'/../../config/travel-services.php' => config_path('travel-services.php'),
        ], 'travel-config');
    }
}
