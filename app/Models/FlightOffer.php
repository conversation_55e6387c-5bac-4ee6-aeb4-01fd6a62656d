<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Flight Offer Model
 *
 * Represents a flight offer returned by travel service providers
 */
class FlightOffer extends Model
{
    use HasFactory;

    protected $fillable = [
        'flight_search_id',
        'provider',
        'external_id',
        'offer_data',
        'total_amount',
        'total_currency',
        'base_amount',
        'base_currency',
        'tax_amount',
        'tax_currency',
        'slices',
        'passengers',
        'owner_airline_code',
        'owner_airline_name',
        'passenger_identity_documents_required',
        'supported_loyalty_programmes',
        'conditions',
        'expires_at',
        'live_mode',
        'status',
        'metadata',
    ];

    protected $casts = [
        'offer_data' => 'array',
        'slices' => 'array',
        'passengers' => 'array',
        'supported_loyalty_programmes' => 'array',
        'conditions' => 'array',
        'metadata' => 'array',
        'expires_at' => 'datetime',
        'passenger_identity_documents_required' => 'boolean',
        'live_mode' => 'boolean',
        'total_amount' => 'decimal:2',
        'base_amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
    ];

    /**
     * Get the flight searches that include this offer (many-to-many)
     */
    public function flightSearches(): BelongsToMany
    {
        return $this->belongsToMany(FlightSearch::class, 'flight_search_offers')
                    ->withTimestamps();
    }

    /**
     * Get the flight search that owns this offer (legacy - direct relationship)
     */
    public function flightSearch(): BelongsTo
    {
        return $this->belongsTo(FlightSearch::class);
    }

    /**
     * Get the bookings for this offer
     */
    public function bookings(): HasMany
    {
        return $this->hasMany(FlightBooking::class);
    }

    /**
     * Check if offer is still available
     */
    public function isAvailable(): bool
    {
        return $this->status === 'available' &&
               ($this->expires_at === null || $this->expires_at > now());
    }

    /**
     * Check if offer has expired
     */
    public function isExpired(): bool
    {
        return $this->status === 'expired' ||
               ($this->expires_at !== null && $this->expires_at < now());
    }

    /**
     * Check if offer is booked
     */
    public function isBooked(): bool
    {
        return $this->status === 'booked';
    }

    /**
     * Mark offer as expired
     */
    public function markExpired(): void
    {
        $this->update(['status' => 'expired']);
    }

    /**
     * Mark offer as booked
     */
    public function markBooked(): void
    {
        $this->update(['status' => 'booked']);
    }

    /**
     * Get offer summary
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'external_id' => $this->external_id,
            'provider' => $this->provider,
            'total_amount' => $this->total_amount,
            'total_currency' => $this->total_currency,
            'owner_airline_code' => $this->owner_airline_code,
            'owner_airline_name' => $this->owner_airline_name,
            'expires_at' => $this->expires_at?->format('c'),
            'status' => $this->status,
            'live_mode' => $this->live_mode,
            'slices_count' => count($this->slices),
            'passengers_count' => count($this->passengers),
        ];
    }

    /**
     * Get flight duration in minutes
     */
    public function getTotalDuration(): int
    {
        $totalMinutes = 0;

        foreach ($this->slices as $slice) {
            if (isset($slice['duration'])) {
                // Parse ISO 8601 duration (PT2H30M format)
                preg_match('/PT(?:(\d+)H)?(?:(\d+)M)?/', $slice['duration'], $matches);
                $hours = isset($matches[1]) ? (int)$matches[1] : 0;
                $minutes = isset($matches[2]) ? (int)$matches[2] : 0;
                $totalMinutes += $hours * 60 + $minutes;
            }
        }

        return $totalMinutes;
    }

    /**
     * Get number of stops/connections
     */
    public function getTotalStops(): int
    {
        $totalStops = 0;

        foreach ($this->slices as $slice) {
            if (isset($slice['segments'])) {
                // Number of segments minus 1 equals number of stops
                $totalStops += max(0, count($slice['segments']) - 1);
            }
        }

        return $totalStops;
    }

    /**
     * Check if this is a direct flight (no stops)
     */
    public function isDirect(): bool
    {
        return $this->getTotalStops() === 0;
    }
}
