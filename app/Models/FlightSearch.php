<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Flight Search Model
 *
 * Represents a flight search request made to travel service providers
 */
class FlightSearch extends Model
{
    use HasFactory;

    protected $fillable = [
        'provider',
        'external_id',
        'search_criteria',
        'passengers',
        'slices',
        'cabin_class',
        'max_connections',
        'private_fares',
        'supplier_timeout',
        'total_offers',
        'status',
        'error_message',
        'metadata',
        'searched_at',
    ];

    protected $casts = [
        'search_criteria' => 'array',
        'passengers' => 'array',
        'slices' => 'array',
        'private_fares' => 'array',
        'metadata' => 'array',
        'searched_at' => 'datetime',
    ];

    /**
     * Get the flight offers for this search (many-to-many)
     */
    public function offers(): BelongsToMany
    {
        return $this->belongsToMany(FlightOffer::class, 'flight_search_offers')
                    ->withTimestamps();
    }

    /**
     * Get the flight offers for this search (legacy - direct relationship)
     */
    public function directOffers(): HasMany
    {
        return $this->hasMany(FlightOffer::class);
    }

    /**
     * Get available offers (not expired)
     */
    public function availableOffers(): BelongsToMany
    {
        return $this->offers()
            ->where('status', 'available')
            ->where(function ($query) {
                $query->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            });
    }

    /**
     * Check if search is completed successfully
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if search failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if search is still pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Mark search as completed
     */
    public function markCompleted(int $totalOffers = 0): void
    {
        $this->update([
            'status' => 'completed',
            'total_offers' => $totalOffers,
            'searched_at' => now(),
        ]);
    }

    /**
     * Mark search as failed
     */
    public function markFailed(string $errorMessage): void
    {
        $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'searched_at' => now(),
        ]);
    }

    /**
     * Get search summary
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'provider' => $this->provider,
            'status' => $this->status,
            'total_offers' => $this->total_offers,
            'passengers_count' => count($this->passengers),
            'slices_count' => count($this->slices),
            'cabin_class' => $this->cabin_class,
            'searched_at' => $this->searched_at?->format('c'),
            'created_at' => $this->created_at->format('c'),
        ];
    }
}
