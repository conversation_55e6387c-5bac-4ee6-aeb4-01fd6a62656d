<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Airport extends Model
{
    use HasFactory;

    protected $fillable = [
        'iata_code',
        'icao_code',
        'name',
        'city_name',
        'iata_city_code',
        'iata_country_code',
        'latitude',
        'longitude',
        'time_zone',
        'type',
        'city_data',
        'provider',
        'external_id',
        'is_major',
    ];

    protected $casts = [
        'city_data' => 'array',
        'latitude' => 'decimal:7',
        'longitude' => 'decimal:7',
        'is_major' => 'boolean',
    ];

    /**
     * Scope for major airports only
     */
    public function scopeMajor($query)
    {
        return $query->where('is_major', true);
    }

    /**
     * Scope for searching by query
     */
    public function scopeSearch($query, $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            $q->where('iata_code', 'like', "%{$searchTerm}%")
              ->orWhere('name', 'like', "%{$searchTerm}%")
              ->orWhere('city_name', 'like', "%{$searchTerm}%");
        });
    }

    /**
     * Scope for filtering by country
     */
    public function scopeCountry($query, $countryCode)
    {
        return $query->where('iata_country_code', $countryCode);
    }
}
