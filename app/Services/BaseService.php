<?php

namespace App\Services;

use App\Contracts\TravelServiceInterface;
use App\Exceptions\ConfigurationException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\RateLimiter;

/**
 * Base service class for all travel service providers
 */
abstract class BaseService implements TravelServiceInterface
{
    protected Client $httpClient;
    protected array $config;
    protected string $providerName;

    public function __construct(array $config)
    {
        $this->config = $config;
        $this->validateConfig();
        $this->initializeHttpClient();
    }

    /**
     * Get the provider name
     *
     * @return string
     */
    public function getProviderName(): string
    {
        return $this->providerName;
    }

    /**
     * Get the service configuration
     *
     * @return array
     */
    public function getConfig(): array
    {
        return $this->config;
    }

    /**
     * Check if the service is available and properly configured
     *
     * @return bool
     */
    public function isAvailable(): bool
    {
        try {
            $this->validateConfig();
            return !empty($this->config['api_key']);
        } catch (ConfigurationException $e) {
            return false;
        }
    }

    /**
     * Test the connection to the service
     *
     * @return bool
     */
    public function testConnection(): bool
    {
        try {
            // This should be implemented by each service provider
            return $this->performConnectionTest();
        } catch (\Exception $e) {
            $this->logError('Connection test failed', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * Get rate limiting information
     *
     * @return array
     */
    public function getRateLimits(): array
    {
        $key = $this->getRateLimitKey();
        
        return [
            'remaining' => RateLimiter::remaining($key, $this->config['rate_limit_max'] ?? 60),
            'reset_time' => RateLimiter::availableIn($key),
        ];
    }

    /**
     * Validate the service configuration
     *
     * @throws ConfigurationException
     */
    protected function validateConfig(): void
    {
        $required = $this->getRequiredConfigKeys();
        
        foreach ($required as $key) {
            if (empty($this->config[$key])) {
                throw new ConfigurationException("Missing required configuration: {$key}");
            }
        }
    }

    /**
     * Initialize the HTTP client
     */
    protected function initializeHttpClient(): void
    {
        $this->httpClient = new Client([
            'base_uri' => $this->config['base_url'],
            'timeout' => $this->config['timeout'] ?? 30,
            'headers' => $this->getDefaultHeaders(),
        ]);
    }

    /**
     * Get default HTTP headers
     *
     * @return array
     */
    protected function getDefaultHeaders(): array
    {
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Accept-Encoding' => 'gzip',
        ];
    }

    /**
     * Make an HTTP request with rate limiting and caching
     *
     * @param string $method
     * @param string $endpoint
     * @param array $options
     * @param string|null $cacheKey
     * @param int|null $cacheTtl
     * @return array
     * @throws \Exception
     */
    protected function makeRequest(
        string $method,
        string $endpoint,
        array $options = [],
        ?string $cacheKey = null,
        ?int $cacheTtl = null
    ): array {
        // Check rate limiting
        $this->checkRateLimit();

        // Check cache first
        if ($cacheKey && $method === 'GET') {
            $cached = $this->getCachedResponse($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }

        try {
            $response = $this->httpClient->request($method, $endpoint, $options);
            $data = json_decode($response->getBody()->getContents(), true);

            // Cache the response if applicable
            if ($cacheKey && $cacheTtl && $method === 'GET') {
                $this->cacheResponse($cacheKey, $data, $cacheTtl);
            }

            $this->logRequest($method, $endpoint, $options, $response->getStatusCode());

            return $data;

        } catch (GuzzleException $e) {
            $this->logError('HTTP request failed', [
                'method' => $method,
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Check rate limiting
     *
     * @throws \Exception
     */
    protected function checkRateLimit(): void
    {
        $key = $this->getRateLimitKey();
        $maxAttempts = $this->config['rate_limit_max'] ?? 60;
        $decayMinutes = $this->config['rate_limit_decay'] ?? 1;

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($key);
            throw new \Exception("Rate limit exceeded. Try again in {$seconds} seconds.");
        }

        RateLimiter::hit($key, $decayMinutes * 60);
    }

    /**
     * Get rate limit key
     *
     * @return string
     */
    protected function getRateLimitKey(): string
    {
        return "travel_service:{$this->providerName}:" . request()->ip();
    }

    /**
     * Get cached response
     *
     * @param string $key
     * @return array|null
     */
    protected function getCachedResponse(string $key): ?array
    {
        if (!config('travel-services.cache.enabled', true)) {
            return null;
        }

        return Cache::get($this->getCacheKey($key));
    }

    /**
     * Cache response
     *
     * @param string $key
     * @param array $data
     * @param int $ttl
     */
    protected function cacheResponse(string $key, array $data, int $ttl): void
    {
        if (!config('travel-services.cache.enabled', true)) {
            return;
        }

        Cache::put($this->getCacheKey($key), $data, $ttl);
    }

    /**
     * Get cache key with prefix
     *
     * @param string $key
     * @return string
     */
    protected function getCacheKey(string $key): string
    {
        $prefix = config('travel-services.cache.prefix', 'travel_');
        return "{$prefix}{$this->providerName}:{$key}";
    }

    /**
     * Log request information
     *
     * @param string $method
     * @param string $endpoint
     * @param array $options
     * @param int $statusCode
     */
    protected function logRequest(string $method, string $endpoint, array $options, int $statusCode): void
    {
        if (!config('travel-services.logging.enabled', true)) {
            return;
        }

        Log::channel(config('travel-services.logging.channel', 'stack'))->info(
            "Travel service API request",
            [
                'provider' => $this->providerName,
                'method' => $method,
                'endpoint' => $endpoint,
                'status_code' => $statusCode,
                'options' => $this->sanitizeLogData($options),
            ]
        );
    }

    /**
     * Log error information
     *
     * @param string $message
     * @param array $context
     */
    protected function logError(string $message, array $context = []): void
    {
        if (!config('travel-services.logging.enabled', true)) {
            return;
        }

        Log::channel(config('travel-services.logging.channel', 'stack'))->error(
            $message,
            array_merge(['provider' => $this->providerName], $context)
        );
    }

    /**
     * Sanitize log data to remove sensitive information
     *
     * @param array $data
     * @return array
     */
    protected function sanitizeLogData(array $data): array
    {
        $sensitive = ['api_key', 'password', 'token', 'secret'];
        
        foreach ($sensitive as $key) {
            if (isset($data[$key])) {
                $data[$key] = '***';
            }
        }

        return $data;
    }

    /**
     * Get required configuration keys
     *
     * @return array
     */
    abstract protected function getRequiredConfigKeys(): array;

    /**
     * Perform connection test specific to the service
     *
     * @return bool
     */
    abstract protected function performConnectionTest(): bool;
}
