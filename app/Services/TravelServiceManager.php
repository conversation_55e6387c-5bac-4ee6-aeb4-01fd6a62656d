<?php

namespace App\Services;

use App\Contracts\FlightServiceInterface;
use App\Contracts\TravelServiceInterface;
use App\Exceptions\ConfigurationException;
use App\Services\Duffel\DuffelService;
use Illuminate\Support\Manager;

/**
 * Travel Service Manager
 * 
 * This manager handles multiple travel service providers and provides
 * a unified interface to interact with them.
 */
class TravelServiceManager extends Manager
{
    /**
     * Get the default driver name
     *
     * @return string
     */
    public function getDefaultDriver(): string
    {
        return config('travel-services.default', 'duffel');
    }

    /**
     * Create Duffel driver instance
     *
     * @return DuffelService
     * @throws ConfigurationException
     */
    public function createDuffelDriver(): DuffelService
    {
        $config = config('travel-services.providers.duffel');
        
        if (!$config) {
            throw new ConfigurationException('Duffel configuration not found');
        }

        return new DuffelService($config);
    }

    /**
     * Get flight service instance
     *
     * @param string|null $provider
     * @return FlightServiceInterface
     */
    public function flights(?string $provider = null): FlightServiceInterface
    {
        $driver = $provider ? $this->driver($provider) : $this->driver();
        
        if (!$driver instanceof FlightServiceInterface) {
            throw new ConfigurationException(
                "Driver {$provider} does not implement FlightServiceInterface"
            );
        }

        return $driver;
    }

    /**
     * Get all available providers
     *
     * @return array
     */
    public function getAvailableProviders(): array
    {
        $providers = config('travel-services.providers', []);
        $available = [];

        foreach ($providers as $name => $config) {
            try {
                $service = $this->driver($name);
                if ($service->isAvailable()) {
                    $available[$name] = [
                        'name' => $name,
                        'driver' => $config['driver'] ?? $name,
                        'available' => true,
                        'test_mode' => $config['test_mode'] ?? false,
                    ];
                }
            } catch (\Exception $e) {
                $available[$name] = [
                    'name' => $name,
                    'driver' => $config['driver'] ?? $name,
                    'available' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $available;
    }

    /**
     * Test connection for a specific provider
     *
     * @param string $provider
     * @return array
     */
    public function testProvider(string $provider): array
    {
        try {
            $service = $this->driver($provider);
            $isAvailable = $service->isAvailable();
            $connectionTest = $isAvailable ? $service->testConnection() : false;

            return [
                'provider' => $provider,
                'available' => $isAvailable,
                'connection_test' => $connectionTest,
                'rate_limits' => $isAvailable ? $service->getRateLimits() : null,
                'config' => $service->getConfig(),
            ];
        } catch (\Exception $e) {
            return [
                'provider' => $provider,
                'available' => false,
                'connection_test' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Test all providers
     *
     * @return array
     */
    public function testAllProviders(): array
    {
        $providers = array_keys(config('travel-services.providers', []));
        $results = [];

        foreach ($providers as $provider) {
            $results[$provider] = $this->testProvider($provider);
        }

        return $results;
    }

    /**
     * Get provider statistics
     *
     * @param string $provider
     * @return array
     */
    public function getProviderStats(string $provider): array
    {
        try {
            $service = $this->driver($provider);
            
            return [
                'provider' => $provider,
                'available' => $service->isAvailable(),
                'rate_limits' => $service->getRateLimits(),
                'config' => [
                    'base_url' => $service->getConfig()['base_url'] ?? null,
                    'timeout' => $service->getConfig()['timeout'] ?? null,
                    'test_mode' => $service->getConfig()['test_mode'] ?? null,
                ],
            ];
        } catch (\Exception $e) {
            return [
                'provider' => $provider,
                'available' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Switch to a different provider temporarily
     *
     * @param string $provider
     * @return TravelServiceInterface
     */
    public function using(string $provider): TravelServiceInterface
    {
        return $this->driver($provider);
    }

    /**
     * Create a custom driver instance
     *
     * @param string $driver
     * @param array $config
     * @return TravelServiceInterface
     */
    public function createCustomDriver(string $driver, array $config): TravelServiceInterface
    {
        switch ($driver) {
            case 'duffel':
                return new DuffelService($config);
            
            // Add more drivers here as they are implemented
            // case 'amadeus':
            //     return new AmadeusService($config);
            
            default:
                throw new ConfigurationException("Unsupported driver: {$driver}");
        }
    }
}
