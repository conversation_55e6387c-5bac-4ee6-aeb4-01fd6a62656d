<?php

namespace App\Services\Duffel;

use App\Contracts\FlightServiceInterface;
use App\DTOs\FlightSearchRequest;
use App\DTOs\FlightSearchResponse;
use App\DTOs\FlightOfferRequest;
use App\DTOs\FlightOfferResponse;
use App\DTOs\FlightBookingRequest;
use App\DTOs\FlightBookingResponse;
use App\Exceptions\ApiRequestException;
use App\Exceptions\InvalidResponseException;
use App\Services\BaseService;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Duffel API service implementation
 */
class DuffelService extends BaseService implements FlightServiceInterface
{
    protected string $providerName = 'duffel';

    /**
     * Get default HTTP headers for Duffel API
     *
     * @return array
     */
    protected function getDefaultHeaders(): array
    {
        return array_merge(parent::getDefaultHeaders(), [
            'Authorization' => 'Bearer ' . $this->config['api_key'],
            'Duffel-Version' => $this->config['version'] ?? 'v2',
        ]);
    }

    /**
     * Get required configuration keys
     *
     * @return array
     */
    protected function getRequiredConfigKeys(): array
    {
        return ['api_key', 'base_url'];
    }

    /**
     * Perform connection test specific to Duffel
     *
     * @return bool
     */
    protected function performConnectionTest(): bool
    {
        try {
            // Test with a simple airports request
            $response = $this->makeRequest('GET', '/air/airports', [
                'query' => ['limit' => 1]
            ]);

            return isset($response['data']) && is_array($response['data']);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Search for flights based on criteria
     *
     * @param FlightSearchRequest $request
     * @return FlightSearchResponse
     * @throws ApiRequestException
     */
    public function searchFlights(FlightSearchRequest $request): FlightSearchResponse
    {
        // Validate request
        $errors = $request->validate();
        if (!empty($errors)) {
            return FlightSearchResponse::failed('Validation failed', $errors);
        }

        try {
            $requestData = [
                'json' => [
                    'data' => $request->toArray()
                ],
                'query' => [
                    'return_offers' => 'true'
                ]
            ];

            // Add supplier timeout if specified
            if ($request->supplierTimeout) {
                $requestData['query']['supplier_timeout'] = $request->supplierTimeout;
            }

            $cacheKey = $this->generateSearchCacheKey($request);
            $cacheTtl = config('travel-services.cache.ttl.search_results', 300);

            $response = $this->makeRequest(
                'POST',
                '/air/offer_requests',
                $requestData,
                $cacheKey,
                $cacheTtl
            );

            if (!isset($response['data'])) {
                throw new InvalidResponseException('Invalid response format from Duffel API');
            }

            $offerRequest = $response['data'];
            $offers = $offerRequest['offers'] ?? [];

            return FlightSearchResponse::success(
                $offerRequest['id'],
                $offers,
                [
                    'total_offers' => count($offers),
                    'created_at' => $offerRequest['created_at'] ?? null,
                    'live_mode' => $offerRequest['live_mode'] ?? false,
                ]
            );

        } catch (GuzzleException $e) {
            $this->logError('Flight search failed', [
                'request' => $request->toArray(),
                'error' => $e->getMessage(),
            ]);

            throw new ApiRequestException(
                'Failed to search flights: ' . $e->getMessage(),
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * Get detailed information about a specific flight offer
     *
     * @param FlightOfferRequest $request
     * @return FlightOfferResponse
     * @throws ApiRequestException
     */
    public function getFlightOffer(FlightOfferRequest $request): FlightOfferResponse
    {
        // Validate request
        $errors = $request->validate();
        if (!empty($errors)) {
            return FlightOfferResponse::failed('Validation failed', $errors);
        }

        try {
            $queryParams = [];
            if ($request->returnAvailableServices) {
                $queryParams['return_available_services'] = 'true';
            }

            $cacheKey = "offer:{$request->offerId}:" . ($request->returnAvailableServices ? 'with_services' : 'basic');
            $cacheTtl = config('travel-services.cache.ttl.offer_details', 1800);

            $response = $this->makeRequest(
                'GET',
                "/air/offers/{$request->offerId}",
                ['query' => $queryParams],
                $cacheKey,
                $cacheTtl
            );

            if (!isset($response['data'])) {
                throw new InvalidResponseException('Invalid response format from Duffel API');
            }

            return FlightOfferResponse::success($response['data']);

        } catch (GuzzleException $e) {
            $this->logError('Get flight offer failed', [
                'offer_id' => $request->offerId,
                'error' => $e->getMessage(),
            ]);

            throw new ApiRequestException(
                'Failed to get flight offer: ' . $e->getMessage(),
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * Book a flight offer
     *
     * @param FlightBookingRequest $request
     * @return FlightBookingResponse
     * @throws ApiRequestException
     */
    public function bookFlight(FlightBookingRequest $request): FlightBookingResponse
    {
        // Validate request
        $errors = $request->validate();
        if (!empty($errors)) {
            return FlightBookingResponse::failed('Validation failed', $errors);
        }

        try {
            $requestData = [
                'json' => [
                    'data' => $request->toArray()
                ]
            ];

            $response = $this->makeRequest('POST', '/air/orders', $requestData);

            if (!isset($response['data'])) {
                throw new InvalidResponseException('Invalid response format from Duffel API');
            }

            $order = $response['data'];

            return FlightBookingResponse::success($order, $order['id']);

        } catch (GuzzleException $e) {
            $this->logError('Flight booking failed', [
                'offer_id' => $request->offerId,
                'error' => $e->getMessage(),
            ]);

            throw new ApiRequestException(
                'Failed to book flight: ' . $e->getMessage(),
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * Get booking details by booking ID
     *
     * @param string $bookingId
     * @return FlightBookingResponse
     * @throws ApiRequestException
     */
    public function getBooking(string $bookingId): FlightBookingResponse
    {
        try {
            $cacheKey = "booking:{$bookingId}";
            $cacheTtl = config('travel-services.cache.ttl.offer_details', 1800);

            $response = $this->makeRequest(
                'GET',
                "/air/orders/{$bookingId}",
                [],
                $cacheKey,
                $cacheTtl
            );

            if (!isset($response['data'])) {
                throw new InvalidResponseException('Invalid response format from Duffel API');
            }

            return FlightBookingResponse::success($response['data'], $bookingId);

        } catch (GuzzleException $e) {
            $this->logError('Get booking failed', [
                'booking_id' => $bookingId,
                'error' => $e->getMessage(),
            ]);

            throw new ApiRequestException(
                'Failed to get booking: ' . $e->getMessage(),
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * Cancel a flight booking
     *
     * @param string $bookingId
     * @return bool
     * @throws ApiRequestException
     */
    public function cancelBooking(string $bookingId): bool
    {
        try {
            $requestData = [
                'json' => [
                    'data' => [
                        'order_id' => $bookingId
                    ]
                ]
            ];

            $response = $this->makeRequest('POST', '/air/order_cancellations', $requestData);

            return isset($response['data']) && !empty($response['data']['id']);

        } catch (GuzzleException $e) {
            $this->logError('Cancel booking failed', [
                'booking_id' => $bookingId,
                'error' => $e->getMessage(),
            ]);

            throw new ApiRequestException(
                'Failed to cancel booking: ' . $e->getMessage(),
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * Get available airports
     *
     * @param string|null $query Search query for airports
     * @return array
     * @throws ApiRequestException
     */
    public function getAirports(?string $query = null): array
    {
        try {
            $queryParams = ['limit' => 200];
            if ($query) {
                $queryParams['name'] = $query;
            }

            $cacheKey = "airports:" . ($query ? md5($query) : 'all');
            $cacheTtl = config('travel-services.cache.ttl.airports', 86400);

            $response = $this->makeRequest(
                'GET',
                '/air/airports',
                ['query' => $queryParams],
                $cacheKey,
                $cacheTtl
            );

            return $response['data'] ?? [];

        } catch (GuzzleException $e) {
            $this->logError('Get airports failed', [
                'query' => $query,
                'error' => $e->getMessage(),
            ]);

            throw new ApiRequestException(
                'Failed to get airports: ' . $e->getMessage(),
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * Get available airlines
     *
     * @return array
     * @throws ApiRequestException
     */
    public function getAirlines(): array
    {
        try {
            $cacheKey = 'airlines:all';
            $cacheTtl = config('travel-services.cache.ttl.airlines', 86400);

            $response = $this->makeRequest(
                'GET',
                '/air/airlines',
                ['query' => ['limit' => 500]],
                $cacheKey,
                $cacheTtl
            );

            return $response['data'] ?? [];

        } catch (GuzzleException $e) {
            $this->logError('Get airlines failed', [
                'error' => $e->getMessage(),
            ]);

            throw new ApiRequestException(
                'Failed to get airlines: ' . $e->getMessage(),
                $e->getCode(),
                $e
            );
        }
    }

    /**
     * Generate cache key for search requests
     *
     * @param FlightSearchRequest $request
     * @return string
     */
    private function generateSearchCacheKey(FlightSearchRequest $request): string
    {
        return 'search:' . md5(json_encode($request->toArray()));
    }
}
