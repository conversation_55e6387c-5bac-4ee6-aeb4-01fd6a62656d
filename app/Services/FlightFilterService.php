<?php

namespace App\Services;

use App\DTOs\FlightFilterRequest;
use Carbon\Carbon;

class FlightFilterService
{
    /**
     * Filter flight offers based on criteria
     */
    public function filterOffers(array $offers, FlightFilterRequest $filter): array
    {
        $filteredOffers = $offers;

        // Apply each filter
        if ($filter->airlines) {
            $filteredOffers = $this->filterByAirlines($filteredOffers, $filter->airlines);
        }

        if ($filter->excludeAirlines) {
            $filteredOffers = $this->excludeAirlines($filteredOffers, $filter->excludeAirlines);
        }

        if ($filter->stops !== null) {
            $filteredOffers = $this->filterByStops($filteredOffers, $filter->stops);
        }

        if ($filter->directFlightsOnly) {
            $filteredOffers = $this->filterDirectFlights($filteredOffers);
        }

        if ($filter->cabinClasses) {
            $filteredOffers = $this->filterByCabinClass($filteredOffers, $filter->cabinClasses);
        }

        if ($filter->minPrice || $filter->maxPrice) {
            $filteredOffers = $this->filterByPrice($filteredOffers, $filter->minPrice, $filter->maxPrice, $filter->currency);
        }

        if ($filter->departureTimeRanges) {
            $filteredOffers = $this->filterByDepartureTime($filteredOffers, $filter->departureTimeRanges);
        }

        if ($filter->arrivalTimeRanges) {
            $filteredOffers = $this->filterByArrivalTime($filteredOffers, $filter->arrivalTimeRanges);
        }

        if ($filter->minDuration || $filter->maxDuration) {
            $filteredOffers = $this->filterByDuration($filteredOffers, $filter->minDuration, $filter->maxDuration);
        }

        if ($filter->aircraft) {
            $filteredOffers = $this->filterByAircraft($filteredOffers, $filter->aircraft);
        }

        if ($filter->refundable !== null) {
            $filteredOffers = $this->filterByRefundable($filteredOffers, $filter->refundable);
        }

        if ($filter->changeable !== null) {
            $filteredOffers = $this->filterByChangeable($filteredOffers, $filter->changeable);
        }

        if ($filter->baggageIncluded) {
            $filteredOffers = $this->filterByBaggage($filteredOffers, $filter->baggageIncluded);
        }

        // Sort results
        $filteredOffers = $this->sortOffers($filteredOffers, $filter->sortBy, $filter->sortOrder);

        return $filteredOffers;
    }

    /**
     * Extract filter options from offers for frontend
     */
    public function extractFilterOptions(array $offers): array
    {
        $airlines = [];
        $stops = [];
        $cabinClasses = [];
        $aircraft = [];
        $prices = [];
        $durations = [];
        $currencies = [];

        foreach ($offers as $offer) {
            // Extract airlines
            $ownerCode = $offer['owner']['iata_code'] ?? null;
            if ($ownerCode) {
                $airlines[$ownerCode] = [
                    'code' => $ownerCode,
                    'name' => $offer['owner']['name'] ?? $ownerCode,
                ];
            }

            // Extract price and currency
            if (isset($offer['total_amount'])) {
                $prices[] = (float) $offer['total_amount'];
            }
            if (isset($offer['total_currency'])) {
                $currencies[] = $offer['total_currency'];
            }

            // Extract duration and stops from slices
            foreach ($offer['slices'] ?? [] as $slice) {
                // Duration
                if (isset($slice['duration'])) {
                    $durations[] = $this->parseDuration($slice['duration']);
                }

                // Stops (segments - 1)
                $segmentCount = count($slice['segments'] ?? []);
                if ($segmentCount > 0) {
                    $stops[] = $segmentCount - 1;
                }

                // Cabin classes and aircraft
                foreach ($slice['segments'] ?? [] as $segment) {
                    foreach ($segment['passengers'] ?? [] as $passenger) {
                        if (isset($passenger['cabin_class'])) {
                            $cabinClasses[] = $passenger['cabin_class'];
                        }
                    }

                    if (isset($segment['aircraft']['iata_code'])) {
                        $aircraft[] = $segment['aircraft']['iata_code'];
                    }
                }
            }
        }

        return [
            'airlines' => array_values(array_unique($airlines, SORT_REGULAR)),
            'stops' => array_values(array_unique($stops)),
            'cabin_classes' => array_values(array_unique($cabinClasses)),
            'aircraft' => array_values(array_unique($aircraft)),
            'price_range' => [
                'min' => !empty($prices) ? min($prices) : 0,
                'max' => !empty($prices) ? max($prices) : 0,
                'currency' => !empty($currencies) ? $currencies[0] : 'USD', // Use first currency found
            ],
            'duration_range' => [
                'min' => !empty($durations) ? min($durations) : 0,
                'max' => !empty($durations) ? max($durations) : 0,
            ],
        ];
    }

    private function filterByAirlines(array $offers, array $airlines): array
    {
        return array_filter($offers, function ($offer) use ($airlines) {
            $ownerCode = $offer['owner']['iata_code'] ?? null;
            return $ownerCode && in_array($ownerCode, $airlines);
        });
    }

    private function excludeAirlines(array $offers, array $excludeAirlines): array
    {
        return array_filter($offers, function ($offer) use ($excludeAirlines) {
            $ownerCode = $offer['owner']['iata_code'] ?? null;
            return !$ownerCode || !in_array($ownerCode, $excludeAirlines);
        });
    }

    private function filterByStops(array $offers, array $allowedStops): array
    {
        return array_filter($offers, function ($offer) use ($allowedStops) {
            foreach ($offer['slices'] ?? [] as $slice) {
                $segmentCount = count($slice['segments'] ?? []);
                $stops = $segmentCount > 0 ? $segmentCount - 1 : 0;
                if (in_array($stops, $allowedStops)) {
                    return true;
                }
            }
            return false;
        });
    }

    private function filterDirectFlights(array $offers): array
    {
        return array_filter($offers, function ($offer) {
            foreach ($offer['slices'] ?? [] as $slice) {
                $segmentCount = count($slice['segments'] ?? []);
                if ($segmentCount !== 1) {
                    return false;
                }
            }
            return true;
        });
    }

    private function filterByCabinClass(array $offers, array $cabinClasses): array
    {
        return array_filter($offers, function ($offer) use ($cabinClasses) {
            foreach ($offer['slices'] ?? [] as $slice) {
                foreach ($slice['segments'] ?? [] as $segment) {
                    foreach ($segment['passengers'] ?? [] as $passenger) {
                        if (isset($passenger['cabin_class']) && in_array($passenger['cabin_class'], $cabinClasses)) {
                            return true;
                        }
                    }
                }
            }
            return false;
        });
    }

    private function filterByPrice(array $offers, ?int $minPrice, ?int $maxPrice, string $currency): array
    {
        return array_filter($offers, function ($offer) use ($minPrice, $maxPrice, $currency) {
            $price = (float) ($offer['total_amount'] ?? 0);
            $offerCurrency = $offer['total_currency'] ?? 'USD';

            // For now, assume same currency. In production, you'd convert currencies
            if ($offerCurrency !== $currency) {
                return true; // Skip currency conversion for now
            }

            if ($minPrice && $price < $minPrice) {
                return false;
            }

            if ($maxPrice && $price > $maxPrice) {
                return false;
            }

            return true;
        });
    }

    private function filterByDepartureTime(array $offers, array $timeRanges): array
    {
        return array_filter($offers, function ($offer) use ($timeRanges) {
            foreach ($offer['slices'] ?? [] as $slice) {
                foreach ($slice['segments'] ?? [] as $segment) {
                    $departureTime = $segment['departing_at'] ?? null;
                    if ($departureTime && $this->isTimeInRanges($departureTime, $timeRanges)) {
                        return true;
                    }
                }
            }
            return false;
        });
    }

    private function filterByArrivalTime(array $offers, array $timeRanges): array
    {
        return array_filter($offers, function ($offer) use ($timeRanges) {
            foreach ($offer['slices'] ?? [] as $slice) {
                foreach ($slice['segments'] ?? [] as $segment) {
                    $arrivalTime = $segment['arriving_at'] ?? null;
                    if ($arrivalTime && $this->isTimeInRanges($arrivalTime, $timeRanges)) {
                        return true;
                    }
                }
            }
            return false;
        });
    }

    private function filterByDuration(array $offers, ?int $minDuration, ?int $maxDuration): array
    {
        return array_filter($offers, function ($offer) use ($minDuration, $maxDuration) {
            foreach ($offer['slices'] ?? [] as $slice) {
                $duration = $this->parseDuration($slice['duration'] ?? '');
                
                if ($minDuration && $duration < $minDuration) {
                    continue;
                }

                if ($maxDuration && $duration > $maxDuration) {
                    continue;
                }

                return true;
            }
            return false;
        });
    }

    private function filterByAircraft(array $offers, array $aircraftTypes): array
    {
        return array_filter($offers, function ($offer) use ($aircraftTypes) {
            foreach ($offer['slices'] ?? [] as $slice) {
                foreach ($slice['segments'] ?? [] as $segment) {
                    $aircraftCode = $segment['aircraft']['iata_code'] ?? null;
                    if ($aircraftCode && in_array($aircraftCode, $aircraftTypes)) {
                        return true;
                    }
                }
            }
            return false;
        });
    }

    private function filterByRefundable(array $offers, bool $refundable): array
    {
        return array_filter($offers, function ($offer) use ($refundable) {
            $conditions = $offer['conditions'] ?? [];
            $isRefundable = isset($conditions['refund_before_departure']['allowed']) 
                && $conditions['refund_before_departure']['allowed'];
            
            return $refundable ? $isRefundable : !$isRefundable;
        });
    }

    private function filterByChangeable(array $offers, bool $changeable): array
    {
        return array_filter($offers, function ($offer) use ($changeable) {
            $conditions = $offer['conditions'] ?? [];
            $isChangeable = isset($conditions['change_before_departure']['allowed']) 
                && $conditions['change_before_departure']['allowed'];
            
            return $changeable ? $isChangeable : !$isChangeable;
        });
    }

    private function filterByBaggage(array $offers, array $baggageTypes): array
    {
        return array_filter($offers, function ($offer) use ($baggageTypes) {
            foreach ($offer['slices'] ?? [] as $slice) {
                foreach ($slice['segments'] ?? [] as $segment) {
                    foreach ($segment['passengers'] ?? [] as $passenger) {
                        $baggages = $passenger['baggages'] ?? [];
                        foreach ($baggages as $baggage) {
                            if (in_array($baggage['type'], $baggageTypes)) {
                                return true;
                            }
                        }
                    }
                }
            }
            return false;
        });
    }

    private function sortOffers(array $offers, string $sortBy, string $sortOrder): array
    {
        usort($offers, function ($a, $b) use ($sortBy, $sortOrder) {
            $valueA = $this->getSortValue($a, $sortBy);
            $valueB = $this->getSortValue($b, $sortBy);

            $comparison = $valueA <=> $valueB;
            return $sortOrder === 'desc' ? -$comparison : $comparison;
        });

        return $offers;
    }

    private function getSortValue($offer, string $sortBy)
    {
        switch ($sortBy) {
            case 'price':
                return (float) ($offer['total_amount'] ?? 0);
            
            case 'duration':
                $totalDuration = 0;
                foreach ($offer['slices'] ?? [] as $slice) {
                    $totalDuration += $this->parseDuration($slice['duration'] ?? '');
                }
                return $totalDuration;
            
            case 'departure_time':
                $firstSlice = $offer['slices'][0] ?? null;
                $firstSegment = $firstSlice['segments'][0] ?? null;
                return $firstSegment['departing_at'] ?? '';
            
            case 'arrival_time':
                $lastSlice = end($offer['slices']) ?: null;
                $lastSegment = $lastSlice ? end($lastSlice['segments']) : null;
                return $lastSegment['arriving_at'] ?? '';
            
            default:
                return 0;
        }
    }

    private function parseDuration(string $duration): int
    {
        // Parse ISO 8601 duration (PT2H30M) to minutes
        if (preg_match('/PT(?:(\d+)H)?(?:(\d+)M)?/', $duration, $matches)) {
            $hours = (int) ($matches[1] ?? 0);
            $minutes = (int) ($matches[2] ?? 0);
            return ($hours * 60) + $minutes;
        }
        return 0;
    }

    private function isTimeInRanges(string $datetime, array $timeRanges): bool
    {
        $time = Carbon::parse($datetime)->format('H:i');
        
        foreach ($timeRanges as $range) {
            $start = $range['start'] ?? '00:00';
            $end = $range['end'] ?? '23:59';
            
            if ($time >= $start && $time <= $end) {
                return true;
            }
        }
        
        return false;
    }
}
