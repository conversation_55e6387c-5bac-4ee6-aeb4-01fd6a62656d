<?php

namespace App\Exceptions;

use Exception;

/**
 * Base exception for travel service operations
 */
class TravelServiceException extends Exception
{
    protected array $context = [];

    public function __construct(
        string $message = '',
        int $code = 0,
        ?Exception $previous = null,
        array $context = []
    ) {
        parent::__construct($message, $code, $previous);
        $this->context = $context;
    }

    /**
     * Get the exception context
     *
     * @return array
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * Set the exception context
     *
     * @param array $context
     * @return self
     */
    public function setContext(array $context): self
    {
        $this->context = $context;
        return $this;
    }
}

/**
 * Exception thrown when API request fails
 */
class ApiRequestException extends TravelServiceException
{
    //
}

/**
 * Exception thrown when API response is invalid
 */
class InvalidResponseException extends TravelServiceException
{
    //
}

/**
 * Exception thrown when service configuration is invalid
 */
class ConfigurationException extends TravelServiceException
{
    //
}

/**
 * Exception thrown when rate limit is exceeded
 */
class RateLimitException extends TravelServiceException
{
    //
}

/**
 * Exception thrown when validation fails
 */
class ValidationException extends TravelServiceException
{
    //
}
