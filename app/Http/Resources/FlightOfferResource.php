<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FlightOfferResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'external_id' => $this->external_id,
            'provider' => $this->provider,
            'status' => $this->status,
            'pricing' => [
                'total_amount' => $this->total_amount,
                'total_currency' => $this->total_currency,
                'base_amount' => $this->base_amount,
                'base_currency' => $this->base_currency,
                'tax_amount' => $this->tax_amount,
                'tax_currency' => $this->tax_currency,
            ],
            'airline' => [
                'code' => $this->owner_airline_code,
                'name' => $this->owner_airline_name,
            ],
            'flight_details' => [
                'slices' => $this->slices,
                'passengers' => $this->passengers,
                'total_duration_minutes' => $this->getTotalDuration(),
                'total_stops' => $this->getTotalStops(),
                'is_direct' => $this->isDirect(),
            ],
            'booking_info' => [
                'passenger_identity_documents_required' => $this->passenger_identity_documents_required,
                'supported_loyalty_programmes' => $this->supported_loyalty_programmes,
                'conditions' => $this->conditions,
                'expires_at' => $this->expires_at?->format('c'),
                'live_mode' => $this->live_mode,
            ],
            'metadata' => $this->metadata,
            'created_at' => $this->created_at->format('c'),
            'updated_at' => $this->updated_at->format('c'),

            // Include full offer data if requested
            'offer_data' => $this->when($request->get('include_full_data'), $this->offer_data),
        ];
    }
}
