<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FlightSearchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'provider' => $this->provider,
            'external_id' => $this->external_id,
            'status' => $this->status,
            'search_criteria' => [
                'passengers' => $this->passengers,
                'slices' => $this->slices,
                'cabin_class' => $this->cabin_class,
                'max_connections' => $this->max_connections,
                'private_fares' => $this->private_fares,
                'supplier_timeout' => $this->supplier_timeout,
            ],
            'results' => [
                'total_offers' => $this->total_offers,
                'offers_available' => $this->offers()->where('status', 'available')->count(),
                'offers_expired' => $this->offers()->where('status', 'expired')->count(),
            ],
            'error_message' => $this->when($this->status === 'failed', $this->error_message),
            'metadata' => $this->metadata,
            'searched_at' => $this->searched_at?->format('c'),
            'created_at' => $this->created_at->format('c'),
            'updated_at' => $this->updated_at->format('c'),

            // Include offers if requested
            'offers' => FlightOfferResource::collection($this->whenLoaded('offers')),
        ];
    }
}
