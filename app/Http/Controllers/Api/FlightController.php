<?php

namespace App\Http\Controllers\Api;

use App\DTOs\FlightSearchRequest as FlightSearchDTO;
use App\DTOs\FlightOfferRequest;
use App\DTOs\FlightBookingRequest;
use App\DTOs\FlightFilterRequest;
use App\Http\Controllers\Controller;
use App\Http\Requests\FlightSearchRequest;
use App\Http\Resources\FlightSearchResource;
use App\Http\Resources\FlightOfferResource;
use App\Models\FlightSearch;
use App\Models\FlightOffer;
use App\Models\FlightBooking;
use App\Models\Airport;
use App\Services\TravelServiceManager;
use App\Services\FlightFilterService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Flight Controller
 *
 * Handles flight search, booking, and management operations
 */
class FlightController extends Controller
{
    public function __construct(
        private TravelServiceManager $travelManager,
        private FlightFilterService $filterService
    ) {}

    /**
     * Search for flights
     */
    public function search(FlightSearchRequest $request): JsonResponse
    {
        try {
            $provider = $request->get('provider', config('travel-services.default'));
            $flightService = $this->travelManager->flights($provider);

            // Create search record
            $search = FlightSearch::create([
                'provider' => $provider,
                'search_criteria' => $request->validated(),
                'passengers' => $request->get('passengers'),
                'slices' => $request->get('slices'),
                'cabin_class' => $request->get('cabin_class'),
                'max_connections' => $request->get('max_connections'),
                'private_fares' => $request->get('private_fares'),
                'supplier_timeout' => $request->get('supplier_timeout'),
                'status' => 'pending',
            ]);

            // Create DTO and search
            $searchDTO = FlightSearchDTO::fromArray($request->validated());
            $response = $flightService->searchFlights($searchDTO);

            if ($response->success) {
                // Update search record
                $search->update([
                    'external_id' => $response->requestId,
                    'status' => 'completed',
                    'total_offers' => count($response->offers),
                    'searched_at' => now(),
                ]);

                // Store offers using updateOrCreate to handle duplicates
                $offerIds = [];
                foreach ($response->offers as $offerData) {
                    $offer = FlightOffer::updateOrCreate(
                        [
                            'provider' => $provider,
                            'external_id' => $offerData['id'],
                        ],
                        [
                            'offer_data' => $offerData,
                            'total_amount' => $offerData['total_amount'],
                            'total_currency' => $offerData['total_currency'],
                            'base_amount' => $offerData['base_amount'] ?? null,
                            'base_currency' => $offerData['base_currency'] ?? null,
                            'tax_amount' => $offerData['tax_amount'] ?? null,
                            'tax_currency' => $offerData['tax_currency'] ?? null,
                            'slices' => $offerData['slices'],
                            'passengers' => $offerData['passengers'],
                            'owner_airline_code' => $offerData['owner']['iata_code'] ?? null,
                            'owner_airline_name' => $offerData['owner']['name'] ?? null,
                            'passenger_identity_documents_required' => $offerData['passenger_identity_documents_required'] ?? false,
                            'supported_loyalty_programmes' => $offerData['supported_loyalty_programmes'] ?? null,
                            'conditions' => $offerData['conditions'] ?? null,
                            'expires_at' => isset($offerData['expires_at']) ? \Carbon\Carbon::parse($offerData['expires_at']) : null,
                            'live_mode' => $offerData['live_mode'] ?? false,
                            'status' => 'available',
                        ]
                    );

                    $offerIds[] = $offer->id;
                }

                // Attach offers to this search using pivot table
                $search->offers()->sync($offerIds);
                $search->offers()->sync($offerIds);

                return response()->json([
                    'success' => true,
                    'data' => new FlightSearchResource($search->load('offers')),
                    'meta' => $response->metadata,
                ]);
            } else {
                $search->markFailed($response->message ?? 'Search failed');

                return response()->json([
                    'success' => false,
                    'message' => $response->message,
                    'errors' => $response->errors,
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Flight search failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get flight search details with filter options
     */
    public function getSearch(FlightSearch $search): JsonResponse
    {
        $search->load('offers');

        // Extract filter options from all offers
        $allOffers = $search->offers->map(fn($offer) => $offer->offer_data)->toArray();
        $filterOptions = $this->filterService->extractFilterOptions($allOffers);

        return response()->json([
            'success' => true,
            'data' => [
                'search' => new FlightSearchResource($search),
                'filter_options' => $filterOptions,
            ],
            'meta' => [
                'total_offers' => $search->offers->count(),
            ]
        ]);
    }

    /**
     * Get flight searches with pagination
     */
    public function getSearches(Request $request): JsonResponse
    {
        $searches = FlightSearch::with('offers')
            ->when($request->get('status'), fn($q, $status) => $q->where('status', $status))
            ->when($request->get('provider'), fn($q, $provider) => $q->where('provider', $provider))
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => FlightSearchResource::collection($searches),
            'meta' => [
                'pagination' => [
                    'current_page' => $searches->currentPage(),
                    'last_page' => $searches->lastPage(),
                    'per_page' => $searches->perPage(),
                    'total' => $searches->total(),
                ]
            ]
        ]);
    }

    /**
     * Get flight offer details
     */
    public function getOffer(FlightOffer $offer): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => new FlightOfferResource($offer),
        ]);
    }

    /**
     * Get detailed flight offer information from provider
     */
    public function getOfferDetails(FlightOffer $offer, Request $request): JsonResponse
    {
        try {
            $flightService = $this->travelManager->flights($offer->provider);

            $offerRequest = new FlightOfferRequest(
                $offer->external_id,
                $request->boolean('return_available_services', false)
            );

            $response = $flightService->getFlightOffer($offerRequest);

            if ($response->success) {
                return response()->json([
                    'success' => true,
                    'data' => $response->offer,
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $response->message,
                    'errors' => $response->errors,
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get offer details',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get offers for a specific search
     */
    public function getSearchOffers(FlightSearch $search, Request $request): JsonResponse
    {
        try {
            // Get all offers for this search
            $allOffers = $search->offers->map(fn($offer) => $offer->offer_data)->toArray();

            // Apply filters if provided
            $filterData = $request->except(['page', 'per_page']);
            $filteredOffers = $allOffers;

            if (!empty($filterData)) {
                $filter = FlightFilterRequest::fromRequest($filterData);
                $validationErrors = $filter->validate();

                if (!empty($validationErrors)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid filter parameters',
                        'errors' => $validationErrors,
                    ], 400);
                }

                $filteredOffers = $this->filterService->filterOffers($allOffers, $filter);
            }

            // Pagination
            $page = (int) $request->get('page', 1);
            $perPage = min((int) $request->get('per_page', 20), 100);
            $total = count($filteredOffers);
            $offset = ($page - 1) * $perPage;
            $paginatedOffers = array_slice($filteredOffers, $offset, $perPage);

            // Get filter options for frontend
            $filterOptions = $this->filterService->extractFilterOptions($allOffers);

            return response()->json([
                'success' => true,
                'data' => [
                    'offers' => $paginatedOffers,
                    'filter_options' => $filterOptions,
                    'applied_filters' => $filterData,
                ],
                'meta' => [
                    'pagination' => [
                        'current_page' => $page,
                        'last_page' => ceil($total / $perPage),
                        'per_page' => $perPage,
                        'total' => $total,
                        'from' => $offset + 1,
                        'to' => min($offset + $perPage, $total),
                    ],
                    'search_id' => $search->id,
                    'original_total' => count($allOffers),
                    'filtered_total' => $total,
                    'filtered' => !empty($filterData),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get offers',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get airports (filtered for major airports)
     */
    public function getAirports(Request $request): JsonResponse
    {
        try {
            $query = $request->get('query');
            $country = $request->get('country');
            $limit = $request->get('limit', 50);

            $airportsQuery = Airport::query();

            // If there's a search query, search all airports
            if ($query) {
                $airportsQuery->search($query);
            } else {
                // Without search, show only major airports
                $airportsQuery->major();
            }

            // Filter by country if provided
            if ($country) {
                $airportsQuery->country($country);
            }

            $airports = $airportsQuery
                ->orderBy('is_major', 'desc')
                ->orderBy('city_name')
                ->limit($limit)
                ->get()
                ->map(function ($airport) {
                    return [
                        'iata_code' => $airport->iata_code,
                        'icao_code' => $airport->icao_code,
                        'name' => $airport->name,
                        'city_name' => $airport->city_name,
                        'iata_city_code' => $airport->iata_city_code,
                        'iata_country_code' => $airport->iata_country_code,
                        'latitude' => $airport->latitude,
                        'longitude' => $airport->longitude,
                        'time_zone' => $airport->time_zone,
                        'type' => $airport->type,
                        'city' => $airport->city_data,
                        'is_major' => $airport->is_major,
                        'id' => $airport->external_id ?? "arp_{$airport->iata_code}_" . strtolower($airport->iata_country_code),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $airports,
                'meta' => [
                    'total' => $airports->count(),
                    'query' => $query,
                    'country' => $country,
                    'limit' => $limit,
                    'source' => 'database',
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get airports',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Filter airports to show only major ones
     */
    private function filterMajorAirports(array $airports, ?string $query = null): array
    {
        // Major airport IATA codes (international and major domestic airports)
        $majorAirportCodes = [
            // Major International Hubs
            'LHR', 'LGW', 'STN', 'LTN', 'SEN', // London
            'CDG', 'ORY', // Paris
            'FRA', 'MUC', // Germany
            'AMS', 'BRU', // Netherlands/Belgium
            'MAD', 'BCN', // Spain
            'FCO', 'MXP', 'LIN', // Italy
            'ZUR', 'GVA', // Switzerland
            'VIE', 'PRG', // Austria/Czech
            'ARN', 'CPH', 'OSL', // Scandinavia
            'DUB', 'EDI', 'MAN', 'BHX', // UK/Ireland

            // North America
            'JFK', 'LGA', 'EWR', // New York
            'LAX', 'SFO', 'SAN', // California
            'ORD', 'MDW', // Chicago
            'DFW', 'IAH', 'DEN', 'ATL', 'MIA', 'BOS', 'SEA', 'LAS', 'PHX',
            'YYZ', 'YVR', 'YUL', // Canada

            // Asia Pacific
            'NRT', 'HND', 'KIX', // Japan
            'ICN', 'GMP', // South Korea
            'PEK', 'PVG', 'CAN', 'SZX', // China
            'HKG', 'TPE', 'SIN', 'BKK', 'KUL', 'CGK', 'MNL',
            'SYD', 'MEL', 'BNE', 'PER', // Australia
            'AKL', 'CHC', // New Zealand
            'DEL', 'BOM', 'BLR', 'MAA', 'CCU', 'HYD', // India

            // Middle East & Africa
            'DXB', 'AUH', 'DOH', 'KWI', 'RUH', 'JED',
            'CAI', 'JNB', 'CPT', 'LOS', 'ACC', 'NBO', 'ADD',

            // South America
            'GRU', 'GIG', 'BSB', 'FOR', // Brazil
            'EZE', 'AEP', // Argentina
            'SCL', 'LIM', 'BOG', 'UIO', 'CCS',
        ];

        $filtered = [];

        foreach ($airports as $airport) {
            $iataCode = $airport['iata_code'] ?? '';
            $cityName = strtolower($airport['city_name'] ?? '');
            $airportName = strtolower($airport['name'] ?? '');

            $shouldInclude = false;

            // Always include major airports
            if (in_array($iataCode, $majorAirportCodes)) {
                $shouldInclude = true;
            }

            // If there's a search query, check for matches
            if ($query && !$shouldInclude) {
                $queryLower = strtolower($query);

                // Include if query matches city or airport name
                if (str_contains($cityName, $queryLower) ||
                    str_contains($airportName, $queryLower) ||
                    str_contains($iataCode, strtoupper($query))) {
                    $shouldInclude = true;
                }
            }

            // Include airports with "International" in the name (without query)
            if (!$query && str_contains($airportName, 'international')) {
                $shouldInclude = true;
            }

            // Include airports that have city information (usually major airports)
            if (!$query && !empty($airport['city'])) {
                $shouldInclude = true;
            }

            // Include capital city airports (without query)
            if (!$query) {
                $capitalCities = [
                    'london', 'paris', 'berlin', 'rome', 'madrid', 'amsterdam', 'brussels',
                    'vienna', 'prague', 'stockholm', 'copenhagen', 'oslo', 'dublin',
                    'washington', 'ottawa', 'tokyo', 'seoul', 'beijing', 'bangkok',
                    'singapore', 'kuala lumpur', 'jakarta', 'manila', 'sydney', 'melbourne',
                    'auckland', 'new delhi', 'mumbai', 'dubai', 'doha', 'riyadh',
                    'cairo', 'johannesburg', 'cape town', 'nairobi', 'addis ababa',
                    'sao paulo', 'rio de janeiro', 'buenos aires', 'santiago', 'lima',
                    'bogota', 'quito', 'caracas', 'mexico city', 'toronto', 'vancouver'
                ];

                foreach ($capitalCities as $capital) {
                    if (str_contains($cityName, $capital)) {
                        $shouldInclude = true;
                        break;
                    }
                }
            }

            if ($shouldInclude) {
                $filtered[] = $airport;
            }
        }

        // Sort by city name for better organization
        usort($filtered, function($a, $b) {
            return strcmp($a['city_name'] ?? '', $b['city_name'] ?? '');
        });

        return $filtered;
    }

    /**
     * Get all airports (unfiltered from database)
     */
    public function getAllAirports(Request $request): JsonResponse
    {
        try {
            $query = $request->get('query');
            $country = $request->get('country');
            $limit = $request->get('limit', 200);

            $airportsQuery = Airport::query();

            // Search if query provided
            if ($query) {
                $airportsQuery->search($query);
            }

            // Filter by country if provided
            if ($country) {
                $airportsQuery->country($country);
            }

            $airports = $airportsQuery
                ->orderBy('is_major', 'desc')
                ->orderBy('city_name')
                ->limit($limit)
                ->get()
                ->map(function ($airport) {
                    return [
                        'iata_code' => $airport->iata_code,
                        'icao_code' => $airport->icao_code,
                        'name' => $airport->name,
                        'city_name' => $airport->city_name,
                        'iata_city_code' => $airport->iata_city_code,
                        'iata_country_code' => $airport->iata_country_code,
                        'latitude' => $airport->latitude,
                        'longitude' => $airport->longitude,
                        'time_zone' => $airport->time_zone,
                        'type' => $airport->type,
                        'city' => $airport->city_data,
                        'is_major' => $airport->is_major,
                        'id' => $airport->external_id ?? "arp_{$airport->iata_code}_" . strtolower($airport->iata_country_code),
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $airports,
                'meta' => [
                    'total' => $airports->count(),
                    'query' => $query,
                    'country' => $country,
                    'limit' => $limit,
                    'source' => 'database',
                    'filtered' => false,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get airports',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Search airports (optimized for autocomplete)
     */
    public function searchAirports(Request $request): JsonResponse
    {
        try {
            $query = $request->get('q', $request->get('query'));
            $limit = min($request->get('limit', 10), 50); // Max 50 for performance

            if (!$query || strlen($query) < 2) {
                return response()->json([
                    'success' => false,
                    'message' => 'Query must be at least 2 characters',
                ], 400);
            }

            $airports = Airport::search($query)
                ->orderBy('is_major', 'desc')
                ->orderBy('city_name')
                ->limit($limit)
                ->get()
                ->map(function ($airport) {
                    return [
                        'iata_code' => $airport->iata_code,
                        'name' => $airport->name,
                        'city_name' => $airport->city_name,
                        'country_code' => $airport->iata_country_code,
                        'is_major' => $airport->is_major,
                        'display_name' => "{$airport->city_name} ({$airport->iata_code}) - {$airport->name}",
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $airports,
                'meta' => [
                    'total' => $airports->count(),
                    'query' => $query,
                    'limit' => $limit,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Search failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get airlines
     */
    public function getAirlines(Request $request): JsonResponse
    {
        try {
            $provider = $request->get('provider', config('travel-services.default'));
            $flightService = $this->travelManager->flights($provider);

            $airlines = $flightService->getAirlines();

            return response()->json([
                'success' => true,
                'data' => $airlines,
                'meta' => [
                    'total' => count($airlines),
                    'provider' => $provider,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get airlines',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
