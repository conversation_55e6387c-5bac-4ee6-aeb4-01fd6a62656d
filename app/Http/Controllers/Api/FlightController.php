<?php

namespace App\Http\Controllers\Api;

use App\DTOs\FlightSearchRequest as FlightSearchDTO;
use App\DTOs\FlightOfferRequest;
use App\DTOs\FlightBookingRequest;
use App\Http\Controllers\Controller;
use App\Http\Requests\FlightSearchRequest;
use App\Http\Resources\FlightSearchResource;
use App\Http\Resources\FlightOfferResource;
use App\Models\FlightSearch;
use App\Models\FlightOffer;
use App\Models\FlightBooking;
use App\Services\TravelServiceManager;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Flight Controller
 *
 * Handles flight search, booking, and management operations
 */
class FlightController extends Controller
{
    public function __construct(
        private TravelServiceManager $travelManager
    ) {}

    /**
     * Search for flights
     */
    public function search(FlightSearchRequest $request): JsonResponse
    {
        try {
            $provider = $request->get('provider', config('travel-services.default'));
            $flightService = $this->travelManager->flights($provider);

            // Create search record
            $search = FlightSearch::create([
                'provider' => $provider,
                'search_criteria' => $request->validated(),
                'passengers' => $request->get('passengers'),
                'slices' => $request->get('slices'),
                'cabin_class' => $request->get('cabin_class'),
                'max_connections' => $request->get('max_connections'),
                'private_fares' => $request->get('private_fares'),
                'supplier_timeout' => $request->get('supplier_timeout'),
                'status' => 'pending',
            ]);

            // Create DTO and search
            $searchDTO = FlightSearchDTO::fromArray($request->validated());
            $response = $flightService->searchFlights($searchDTO);

            if ($response->success) {
                // Update search record
                $search->update([
                    'external_id' => $response->requestId,
                    'status' => 'completed',
                    'total_offers' => count($response->offers),
                    'searched_at' => now(),
                ]);

                // Store offers
                foreach ($response->offers as $offerData) {
                    FlightOffer::create([
                        'flight_search_id' => $search->id,
                        'provider' => $provider,
                        'external_id' => $offerData['id'],
                        'offer_data' => $offerData,
                        'total_amount' => $offerData['total_amount'],
                        'total_currency' => $offerData['total_currency'],
                        'base_amount' => $offerData['base_amount'] ?? null,
                        'base_currency' => $offerData['base_currency'] ?? null,
                        'tax_amount' => $offerData['tax_amount'] ?? null,
                        'tax_currency' => $offerData['tax_currency'] ?? null,
                        'slices' => $offerData['slices'],
                        'passengers' => $offerData['passengers'],
                        'owner_airline_code' => $offerData['owner']['iata_code'] ?? null,
                        'owner_airline_name' => $offerData['owner']['name'] ?? null,
                        'passenger_identity_documents_required' => $offerData['passenger_identity_documents_required'] ?? false,
                        'supported_loyalty_programmes' => $offerData['supported_loyalty_programmes'] ?? null,
                        'conditions' => $offerData['conditions'] ?? null,
                        'expires_at' => isset($offerData['expires_at']) ? \Carbon\Carbon::parse($offerData['expires_at']) : null,
                        'live_mode' => $offerData['live_mode'] ?? false,
                        'status' => 'available',
                    ]);
                }

                return response()->json([
                    'success' => true,
                    'data' => new FlightSearchResource($search->load('offers')),
                    'meta' => $response->metadata,
                ]);
            } else {
                $search->markFailed($response->message ?? 'Search failed');

                return response()->json([
                    'success' => false,
                    'message' => $response->message,
                    'errors' => $response->errors,
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Flight search failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get flight search details
     */
    public function getSearch(FlightSearch $search): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => new FlightSearchResource($search->load('offers')),
        ]);
    }

    /**
     * Get flight searches with pagination
     */
    public function getSearches(Request $request): JsonResponse
    {
        $searches = FlightSearch::with('offers')
            ->when($request->get('status'), fn($q, $status) => $q->where('status', $status))
            ->when($request->get('provider'), fn($q, $provider) => $q->where('provider', $provider))
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => FlightSearchResource::collection($searches),
            'meta' => [
                'pagination' => [
                    'current_page' => $searches->currentPage(),
                    'last_page' => $searches->lastPage(),
                    'per_page' => $searches->perPage(),
                    'total' => $searches->total(),
                ]
            ]
        ]);
    }

    /**
     * Get flight offer details
     */
    public function getOffer(FlightOffer $offer): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => new FlightOfferResource($offer),
        ]);
    }

    /**
     * Get detailed flight offer information from provider
     */
    public function getOfferDetails(FlightOffer $offer, Request $request): JsonResponse
    {
        try {
            $flightService = $this->travelManager->flights($offer->provider);

            $offerRequest = new FlightOfferRequest(
                $offer->external_id,
                $request->boolean('return_available_services', false)
            );

            $response = $flightService->getFlightOffer($offerRequest);

            if ($response->success) {
                return response()->json([
                    'success' => true,
                    'data' => $response->offer,
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $response->message,
                    'errors' => $response->errors,
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get offer details',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get offers for a specific search
     */
    public function getSearchOffers(FlightSearch $search, Request $request): JsonResponse
    {
        $offers = $search->offers()
            ->when($request->get('status'), fn($q, $status) => $q->where('status', $status))
            ->when($request->get('max_price'), fn($q, $price) => $q->where('total_amount', '<=', $price))
            ->when($request->get('airline'), fn($q, $airline) => $q->where('owner_airline_code', $airline))
            ->orderBy($request->get('sort', 'total_amount'), $request->get('direction', 'asc'))
            ->paginate($request->get('per_page', 20));

        return response()->json([
            'success' => true,
            'data' => FlightOfferResource::collection($offers),
            'meta' => [
                'search_id' => $search->id,
                'pagination' => [
                    'current_page' => $offers->currentPage(),
                    'last_page' => $offers->lastPage(),
                    'per_page' => $offers->perPage(),
                    'total' => $offers->total(),
                ]
            ]
        ]);
    }

    /**
     * Get airports
     */
    public function getAirports(Request $request): JsonResponse
    {
        try {
            $provider = $request->get('provider', config('travel-services.default'));
            $flightService = $this->travelManager->flights($provider);

            $airports = $flightService->getAirports($request->get('query'));

            return response()->json([
                'success' => true,
                'data' => $airports,
                'meta' => [
                    'total' => count($airports),
                    'provider' => $provider,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get airports',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get airlines
     */
    public function getAirlines(Request $request): JsonResponse
    {
        try {
            $provider = $request->get('provider', config('travel-services.default'));
            $flightService = $this->travelManager->flights($provider);

            $airlines = $flightService->getAirlines();

            return response()->json([
                'success' => true,
                'data' => $airlines,
                'meta' => [
                    'total' => count($airlines),
                    'provider' => $provider,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get airlines',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
