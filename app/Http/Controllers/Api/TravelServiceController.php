<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\TravelServiceManager;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Travel Service Controller
 *
 * Handles travel service provider management and testing
 */
class TravelServiceController extends Controller
{
    public function __construct(
        private TravelServiceManager $travelManager
    ) {}

    /**
     * Get all available travel service providers
     */
    public function getProviders(): JsonResponse
    {
        try {
            $providers = $this->travelManager->getAvailableProviders();

            return response()->json([
                'success' => true,
                'data' => $providers,
                'meta' => [
                    'total_providers' => count($providers),
                    'available_providers' => count(array_filter($providers, fn($p) => $p['available'])),
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get providers',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test a specific travel service provider
     */
    public function testProvider(string $provider): JsonResponse
    {
        try {
            $result = $this->travelManager->testProvider($provider);

            return response()->json([
                'success' => true,
                'data' => $result,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => "Failed to test provider: {$provider}",
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get statistics for a specific provider
     */
    public function getProviderStats(string $provider): JsonResponse
    {
        try {
            $stats = $this->travelManager->getProviderStats($provider);

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => "Failed to get stats for provider: {$provider}",
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test all travel service providers
     */
    public function testAllProviders(): JsonResponse
    {
        try {
            $results = $this->travelManager->testAllProviders();

            $summary = [
                'total_providers' => count($results),
                'available_providers' => count(array_filter($results, fn($r) => $r['available'])),
                'connection_successful' => count(array_filter($results, fn($r) => $r['connection_test'] ?? false)),
            ];

            return response()->json([
                'success' => true,
                'data' => $results,
                'meta' => $summary,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to test providers',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
