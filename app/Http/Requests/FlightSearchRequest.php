<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FlightSearchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'passengers' => 'required|array|min:1|max:9',
            'passengers.*.type' => 'required_without:passengers.*.age|string|in:adult,child,infant',
            'passengers.*.age' => 'required_without:passengers.*.type|integer|min:0|max:120',
            'passengers.*.given_name' => 'sometimes|string|max:50',
            'passengers.*.family_name' => 'sometimes|string|max:50',
            'passengers.*.fare_type' => 'sometimes|string',
            'passengers.*.loyalty_programme_accounts' => 'sometimes|array',
            'passengers.*.loyalty_programme_accounts.*.airline_iata_code' => 'required_with:passengers.*.loyalty_programme_accounts|string|size:2',
            'passengers.*.loyalty_programme_accounts.*.account_number' => 'required_with:passengers.*.loyalty_programme_accounts|string|max:50',

            'slices' => 'required|array|min:1|max:4',
            'slices.*.origin' => 'required|string|size:3',
            'slices.*.destination' => 'required|string|size:3',
            'slices.*.departure_date' => 'required|date|after_or_equal:today',
            'slices.*.departure_time' => 'sometimes|array',
            'slices.*.departure_time.from' => 'required_with:slices.*.departure_time|date_format:H:i',
            'slices.*.departure_time.to' => 'required_with:slices.*.departure_time|date_format:H:i|after:slices.*.departure_time.from',
            'slices.*.arrival_time' => 'sometimes|array',
            'slices.*.arrival_time.from' => 'required_with:slices.*.arrival_time|date_format:H:i',
            'slices.*.arrival_time.to' => 'required_with:slices.*.arrival_time|date_format:H:i|after:slices.*.arrival_time.from',

            'cabin_class' => 'sometimes|string|in:first,business,premium_economy,economy',
            'max_connections' => 'sometimes|integer|min:0|max:3',
            'supplier_timeout' => 'sometimes|integer|min:2000|max:60000',

            'private_fares' => 'sometimes|array',
            'private_fares.*' => 'array',
            'private_fares.*.*.corporate_code' => 'sometimes|string|max:20',
            'private_fares.*.*.tour_code' => 'sometimes|string|max:20',
            'private_fares.*.*.tracking_reference' => 'sometimes|string|max:50',

            'provider' => 'sometimes|string|in:duffel',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'passengers.required' => 'At least one passenger is required',
            'passengers.min' => 'At least one passenger is required',
            'passengers.max' => 'Maximum 9 passengers allowed',
            'passengers.*.type.required_without' => 'Either passenger type or age must be specified',
            'passengers.*.age.required_without' => 'Either passenger type or age must be specified',
            'slices.required' => 'At least one flight slice is required',
            'slices.min' => 'At least one flight slice is required',
            'slices.max' => 'Maximum 4 flight slices allowed',
            'slices.*.origin.required' => 'Origin airport code is required',
            'slices.*.origin.size' => 'Origin must be a 3-letter airport code',
            'slices.*.destination.required' => 'Destination airport code is required',
            'slices.*.destination.size' => 'Destination must be a 3-letter airport code',
            'slices.*.departure_date.required' => 'Departure date is required',
            'slices.*.departure_date.after_or_equal' => 'Departure date must be today or in the future',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'passengers.*.type' => 'passenger type',
            'passengers.*.age' => 'passenger age',
            'passengers.*.given_name' => 'passenger given name',
            'passengers.*.family_name' => 'passenger family name',
            'slices.*.origin' => 'origin airport',
            'slices.*.destination' => 'destination airport',
            'slices.*.departure_date' => 'departure date',
        ];
    }
}
