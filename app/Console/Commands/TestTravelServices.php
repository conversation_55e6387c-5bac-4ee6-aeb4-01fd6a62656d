<?php

namespace App\Console\Commands;

use App\Services\TravelServiceManager;
use Illuminate\Console\Command;

class TestTravelServices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'travel:test {provider?} {--all}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test travel service providers';

    /**
     * Execute the console command.
     */
    public function handle(TravelServiceManager $travelManager)
    {
        $this->info('🚀 Testing Travel Service Providers');
        $this->newLine();

        if ($this->option('all')) {
            $this->testAllProviders($travelManager);
        } elseif ($provider = $this->argument('provider')) {
            $this->testSingleProvider($travelManager, $provider);
        } else {
            $this->showAvailableProviders($travelManager);
        }
    }

    private function testAllProviders(TravelServiceManager $travelManager): void
    {
        $this->info('Testing all providers...');
        $this->newLine();

        $results = $travelManager->testAllProviders();

        foreach ($results as $provider => $result) {
            $this->displayProviderResult($provider, $result);
        }

        $this->newLine();
        $this->info('✅ All tests completed!');
    }

    private function testSingleProvider(TravelServiceManager $travelManager, string $provider): void
    {
        $this->info("Testing provider: {$provider}");
        $this->newLine();

        try {
            $result = $travelManager->testProvider($provider);
            $this->displayProviderResult($provider, $result);
        } catch (\Exception $e) {
            $this->error("❌ Failed to test {$provider}: {$e->getMessage()}");
        }
    }

    private function showAvailableProviders(TravelServiceManager $travelManager): void
    {
        $this->info('Available Travel Service Providers:');
        $this->newLine();

        try {
            $providers = $travelManager->getAvailableProviders();

            if (empty($providers)) {
                $this->warn('No providers configured.');
                return;
            }

            foreach ($providers as $name => $provider) {
                $status = $provider['available'] ? '✅ Available' : '❌ Unavailable';
                $testMode = $provider['test_mode'] ?? false ? ' (Test Mode)' : '';

                $this->line("• {$name}: {$status}{$testMode}");

                if (!$provider['available'] && isset($provider['error'])) {
                    $this->line("  Error: {$provider['error']}", 'comment');
                }
            }

            $this->newLine();
            $this->info('💡 Use --all to test all providers or specify a provider name');
            $this->info('   Example: php artisan travel:test duffel');
            $this->info('   Example: php artisan travel:test --all');

        } catch (\Exception $e) {
            $this->error("Failed to get providers: {$e->getMessage()}");
        }
    }

    private function displayProviderResult(string $provider, array $result): void
    {
        $available = $result['available'] ? '✅' : '❌';
        $connection = $result['connection_test'] ?? false ? '✅' : '❌';

        $this->line("📦 Provider: {$provider}");
        $this->line("   Available: {$available}");
        $this->line("   Connection: {$connection}");

        if (isset($result['rate_limits'])) {
            $remaining = $result['rate_limits']['remaining'] ?? 'N/A';
            $this->line("   Rate Limit: {$remaining} requests remaining");
        }

        if (isset($result['config']['base_url'])) {
            $this->line("   Base URL: {$result['config']['base_url']}");
        }

        if (isset($result['error'])) {
            $this->line("   Error: {$result['error']}", 'comment');
        }

        $this->newLine();
    }
}
