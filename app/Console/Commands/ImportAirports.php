<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Airport;
use App\Services\TravelServiceManager;

class ImportAirports extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'airports:import {--provider=duffel : The provider to import from} {--force : Force reimport even if airports exist}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import airports from travel service provider';

    protected $travelManager;

    public function __construct(TravelServiceManager $travelManager)
    {
        parent::__construct();
        $this->travelManager = $travelManager;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $provider = $this->option('provider');
        $force = $this->option('force');

        $this->info("Starting airport import from {$provider}...");

        // Check if airports already exist
        if (!$force && Airport::where('provider', $provider)->exists()) {
            if (!$this->confirm('Airports already exist for this provider. Do you want to continue?')) {
                $this->info('Import cancelled.');
                return 0;
            }
        }

        try {
            $flightService = $this->travelManager->flights($provider);

            $this->info('Fetching airports from API...');
            $airports = $flightService->getAirports();

            $this->info('Found ' . count($airports) . ' airports. Starting import...');

            $bar = $this->output->createProgressBar(count($airports));
            $bar->start();

            $majorAirportCodes = $this->getMajorAirportCodes();
            $imported = 0;
            $updated = 0;

            foreach ($airports as $airportData) {
                $isMajor = in_array($airportData['iata_code'] ?? '', $majorAirportCodes);

                $airport = Airport::updateOrCreate(
                    [
                        'iata_code' => $airportData['iata_code'],
                        'provider' => $provider,
                    ],
                    [
                        'icao_code' => $airportData['icao_code'] ?? null,
                        'name' => $airportData['name'],
                        'city_name' => $airportData['city_name'] ?? null,
                        'iata_city_code' => $airportData['iata_city_code'] ?? null,
                        'iata_country_code' => $airportData['iata_country_code'],
                        'latitude' => $airportData['latitude'] ?? null,
                        'longitude' => $airportData['longitude'] ?? null,
                        'time_zone' => $airportData['time_zone'] ?? null,
                        'type' => $airportData['type'] ?? 'airport',
                        'city_data' => $airportData['city'] ?? null,
                        'external_id' => $airportData['id'] ?? null,
                        'is_major' => $isMajor,
                    ]
                );

                if ($airport->wasRecentlyCreated) {
                    $imported++;
                } else {
                    $updated++;
                }

                $bar->advance();
            }

            $bar->finish();
            $this->newLine(2);

            $this->info("Import completed successfully!");
            $this->info("Imported: {$imported} new airports");
            $this->info("Updated: {$updated} existing airports");
            $this->info("Total major airports: " . Airport::major()->count());

        } catch (\Exception $e) {
            $this->error("Import failed: " . $e->getMessage());
            return 1;
        }

        return 0;
    }

    /**
     * Get list of major airport IATA codes
     */
    private function getMajorAirportCodes(): array
    {
        return [
            // Major International Hubs
            'LHR', 'LGW', 'STN', 'LTN', 'SEN', // London
            'CDG', 'ORY', // Paris
            'FRA', 'MUC', // Germany
            'AMS', 'BRU', // Netherlands/Belgium
            'MAD', 'BCN', // Spain
            'FCO', 'MXP', 'LIN', // Italy
            'ZUR', 'GVA', // Switzerland
            'VIE', 'PRG', // Austria/Czech
            'ARN', 'CPH', 'OSL', // Scandinavia
            'DUB', 'EDI', 'MAN', 'BHX', // UK/Ireland

            // North America
            'JFK', 'LGA', 'EWR', // New York
            'LAX', 'SFO', 'SAN', // California
            'ORD', 'MDW', // Chicago
            'DFW', 'IAH', 'DEN', 'ATL', 'MIA', 'BOS', 'SEA', 'LAS', 'PHX',
            'YYZ', 'YVR', 'YUL', // Canada

            // Asia Pacific
            'NRT', 'HND', 'KIX', // Japan
            'ICN', 'GMP', // South Korea
            'PEK', 'PVG', 'CAN', 'SZX', // China
            'HKG', 'TPE', 'SIN', 'BKK', 'KUL', 'CGK', 'MNL',
            'SYD', 'MEL', 'BNE', 'PER', // Australia
            'AKL', 'CHC', // New Zealand
            'DEL', 'BOM', 'BLR', 'MAA', 'CCU', 'HYD', // India

            // Middle East & Africa
            'DXB', 'AUH', 'DOH', 'KWI', 'RUH', 'JED',
            'CAI', 'JNB', 'CPT', 'LOS', 'ACC', 'NBO', 'ADD',

            // South America
            'GRU', 'GIG', 'BSB', 'FOR', // Brazil
            'EZE', 'AEP', // Argentina
            'SCL', 'LIM', 'BOG', 'UIO', 'CCS',
        ];
    }
}
