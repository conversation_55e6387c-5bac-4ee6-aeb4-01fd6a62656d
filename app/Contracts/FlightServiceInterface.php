<?php

namespace App\Contracts;

use App\DTOs\FlightSearchRequest;
use App\DTOs\FlightSearchResponse;
use App\DTOs\FlightOfferRequest;
use App\DTOs\FlightOfferResponse;
use App\DTOs\FlightBookingRequest;
use App\DTOs\FlightBookingResponse;

/**
 * Interface for flight service providers
 * 
 * This interface defines the methods that flight service providers
 * must implement for flight search, booking, and management operations.
 */
interface FlightServiceInterface extends TravelServiceInterface
{
    /**
     * Search for flights based on criteria
     *
     * @param FlightSearchRequest $request
     * @return FlightSearchResponse
     */
    public function searchFlights(FlightSearchRequest $request): FlightSearchResponse;

    /**
     * Get detailed information about a specific flight offer
     *
     * @param FlightOfferRequest $request
     * @return FlightOfferResponse
     */
    public function getFlightOffer(FlightOfferRequest $request): FlightOfferResponse;

    /**
     * Book a flight offer
     *
     * @param FlightBookingRequest $request
     * @return FlightBookingResponse
     */
    public function bookFlight(FlightBookingRequest $request): FlightBookingResponse;

    /**
     * Get booking details by booking ID
     *
     * @param string $bookingId
     * @return FlightBookingResponse
     */
    public function getBooking(string $bookingId): FlightBookingResponse;

    /**
     * Cancel a flight booking
     *
     * @param string $bookingId
     * @return bool
     */
    public function cancelBooking(string $bookingId): bool;

    /**
     * Get available airports
     *
     * @param string|null $query Search query for airports
     * @return array
     */
    public function getAirports(?string $query = null): array;

    /**
     * Get available airlines
     *
     * @return array
     */
    public function getAirlines(): array;
}
