<?php

namespace App\Contracts;

/**
 * Base interface for all travel service providers
 * 
 * This interface defines the common methods that all travel service providers
 * must implement, ensuring consistency across different providers.
 */
interface TravelServiceInterface
{
    /**
     * Get the provider name
     *
     * @return string
     */
    public function getProviderName(): string;

    /**
     * Check if the service is available and properly configured
     *
     * @return bool
     */
    public function isAvailable(): bool;

    /**
     * Get the service configuration
     *
     * @return array
     */
    public function getConfig(): array;

    /**
     * Test the connection to the service
     *
     * @return bool
     */
    public function testConnection(): bool;

    /**
     * Get rate limiting information
     *
     * @return array
     */
    public function getRateLimits(): array;
}
