<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flight_search_offers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('flight_search_id')->constrained()->onDelete('cascade');
            $table->foreignId('flight_offer_id')->constrained()->onDelete('cascade');
            $table->timestamps();

            $table->unique(['flight_search_id', 'flight_offer_id']);
            $table->index('flight_search_id');
            $table->index('flight_offer_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flight_search_offers');
    }
};
