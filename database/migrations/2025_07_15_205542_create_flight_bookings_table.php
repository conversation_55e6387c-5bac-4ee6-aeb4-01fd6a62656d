<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flight_bookings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('flight_offer_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null');
            $table->string('provider')->default('duffel');
            $table->string('external_id'); // Provider's booking/order ID
            $table->string('booking_reference')->nullable(); // Human-readable reference
            $table->json('booking_data'); // Complete booking data from provider
            $table->json('passengers'); // Passenger details used for booking
            $table->json('payments'); // Payment information
            $table->json('services')->nullable(); // Additional services booked
            $table->decimal('total_amount', 10, 2);
            $table->string('total_currency', 3);
            $table->string('status'); // confirmed, cancelled, pending, failed
            $table->string('booking_type')->default('instant'); // instant, hold
            $table->timestamp('booked_at')->nullable();
            $table->timestamp('cancelled_at')->nullable();
            $table->text('cancellation_reason')->nullable();
            $table->json('documents')->nullable(); // Tickets, receipts, etc.
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->unique(['provider', 'external_id']);
            $table->index(['user_id', 'status']);
            $table->index(['booking_reference']);
            $table->index(['status', 'booked_at']);
            $table->index('cancelled_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flight_bookings');
    }
};
