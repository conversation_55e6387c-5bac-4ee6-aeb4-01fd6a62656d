<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('flight_offers', function (Blueprint $table) {
            // Drop the existing unique constraint
            $table->dropUnique(['provider', 'external_id']);

            // Add a new index instead (allows duplicates)
            $table->index(['provider', 'external_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('flight_offers', function (Blueprint $table) {
            // Drop the index
            $table->dropIndex(['provider', 'external_id']);

            // Restore the unique constraint
            $table->unique(['provider', 'external_id']);
        });
    }
};
