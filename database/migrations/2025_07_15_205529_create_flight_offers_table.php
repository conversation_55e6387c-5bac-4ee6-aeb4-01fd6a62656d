<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flight_offers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('flight_search_id')->constrained()->onDelete('cascade');
            $table->string('provider')->default('duffel');
            $table->string('external_id'); // Provider's offer ID
            $table->json('offer_data'); // Complete offer data from provider
            $table->decimal('total_amount', 10, 2);
            $table->string('total_currency', 3);
            $table->decimal('base_amount', 10, 2)->nullable();
            $table->string('base_currency', 3)->nullable();
            $table->decimal('tax_amount', 10, 2)->nullable();
            $table->string('tax_currency', 3)->nullable();
            $table->json('slices'); // Flight slices with segments
            $table->json('passengers'); // Passenger-specific data
            $table->string('owner_airline_code')->nullable(); // Airline IATA code
            $table->string('owner_airline_name')->nullable();
            $table->boolean('passenger_identity_documents_required')->default(false);
            $table->json('supported_loyalty_programmes')->nullable();
            $table->json('conditions')->nullable(); // Change/refund conditions
            $table->timestamp('expires_at')->nullable();
            $table->boolean('live_mode')->default(false);
            $table->string('status')->default('available'); // available, expired, booked
            $table->json('metadata')->nullable();
            $table->timestamps();

            $table->unique(['provider', 'external_id']);
            $table->index(['flight_search_id', 'status']);
            $table->index(['total_amount', 'total_currency']);
            $table->index(['expires_at', 'status']);
            $table->index('owner_airline_code');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flight_offers');
    }
};
