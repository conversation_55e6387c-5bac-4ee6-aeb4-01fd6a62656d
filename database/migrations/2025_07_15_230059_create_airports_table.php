<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('airports', function (Blueprint $table) {
            $table->id();
            $table->string('iata_code', 3)->unique();
            $table->string('icao_code', 4)->nullable();
            $table->string('name');
            $table->string('city_name')->nullable();
            $table->string('iata_city_code', 3)->nullable();
            $table->string('iata_country_code', 2);
            $table->decimal('latitude', 10, 7)->nullable();
            $table->decimal('longitude', 11, 7)->nullable();
            $table->string('time_zone')->nullable();
            $table->string('type')->default('airport'); // airport, city, etc.
            $table->json('city_data')->nullable(); // City information if available
            $table->string('provider')->default('duffel');
            $table->string('external_id')->nullable(); // Provider's internal ID
            $table->boolean('is_major')->default(false); // Flag for major airports
            $table->timestamps();

            $table->index(['iata_code']);
            $table->index(['city_name']);
            $table->index(['iata_country_code']);
            $table->index(['is_major']);
            $table->index(['provider', 'external_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('airports');
    }
};
