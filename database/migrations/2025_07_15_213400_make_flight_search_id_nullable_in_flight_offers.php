<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('flight_offers', function (Blueprint $table) {
            // Make flight_search_id nullable since we're using pivot table now
            $table->foreignId('flight_search_id')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('flight_offers', function (Blueprint $table) {
            // Make flight_search_id required again
            $table->foreignId('flight_search_id')->nullable(false)->change();
        });
    }
};
