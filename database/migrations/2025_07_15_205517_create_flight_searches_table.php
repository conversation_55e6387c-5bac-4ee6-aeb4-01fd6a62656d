<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flight_searches', function (Blueprint $table) {
            $table->id();
            $table->string('provider')->default('duffel');
            $table->string('external_id')->nullable(); // Provider's request ID
            $table->json('search_criteria'); // Original search parameters
            $table->json('passengers'); // Passenger information
            $table->json('slices'); // Flight slices (origin, destination, dates)
            $table->string('cabin_class')->nullable();
            $table->integer('max_connections')->nullable();
            $table->json('private_fares')->nullable();
            $table->integer('supplier_timeout')->nullable();
            $table->integer('total_offers')->default(0);
            $table->string('status')->default('pending'); // pending, completed, failed
            $table->text('error_message')->nullable();
            $table->json('metadata')->nullable(); // Additional provider-specific data
            $table->timestamp('searched_at')->nullable();
            $table->timestamps();

            $table->index(['provider', 'external_id']);
            $table->index(['status', 'created_at']);
            $table->index('searched_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flight_searches');
    }
};
