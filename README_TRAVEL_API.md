# Travel Smart API - Duffel Integration

این پروژه یک API جامع برای سرویس‌های سفر است که با معماری انعطاف‌پذیر طراحی شده تا بتوان به راحتی سرویس‌های مختلف سفر را اضافه کرد.

## ویژگی‌های کلیدی

### 🏗️ معماری انعطاف‌پذیر
- **Service Layer Pattern**: مدیریت API calls
- **Repository Pattern**: ذخیره و بازیابی داده‌ها  
- **Interface-based Architecture**: امکان اضافه کردن سرویس‌های جدید
- **Manager Pattern**: مدیریت یکپارچه سرویس‌ها

### 🛫 سرویس‌های پرواز
- جستجوی پرواز با فیلترهای پیشرفته
- دریافت جزئیات پیشنهادات پرواز
- رزرو و مدیریت بلیط‌ها
- لغو رزرو
- دریافت اطلاعات فرودگاه‌ها و ایرلاین‌ها

### 🔧 قابلیت‌های فنی
- **Caching**: کش کردن نتایج برای بهبود عملکرد
- **Rate Limiting**: محدودیت تعداد درخواست‌ها
- **Logging**: ثبت کامل عملیات‌ها
- **Error Handling**: مدیریت خطاها
- **Validation**: اعتبارسنجی کامل داده‌ها

## نصب و راه‌اندازی

### 1. تنظیمات محیط
```bash
# کپی کردن فایل تنظیمات
cp .env.example .env

# تنظیم API Key دافل
DUFFEL_API_KEY=your_duffel_api_key_here
DUFFEL_TEST_MODE=true
```

### 2. اجرای migrations
```bash
php artisan migrate
```

### 3. تست سرویس‌ها
```bash
# تست تمام سرویس‌ها
curl -X POST http://localhost:8000/api/travel/providers/test-all

# تست سرویس دافل
curl -X GET http://localhost:8000/api/travel/providers/duffel/test
```

## استفاده از API

### 🔍 جستجوی پرواز

```bash
curl -X POST http://localhost:8000/api/flights/search \
  -H "Content-Type: application/json" \
  -d '{
    "passengers": [
      {"type": "adult"}
    ],
    "slices": [
      {
        "origin": "LHR",
        "destination": "JFK", 
        "departure_date": "2024-12-25"
      }
    ],
    "cabin_class": "economy"
  }'
```

### 📋 دریافت نتایج جستجو
```bash
curl -X GET http://localhost:8000/api/flights/searches/{search_id}
```

### 🎫 دریافت جزئیات پیشنهاد
```bash
curl -X GET http://localhost:8000/api/flights/offers/{offer_id}/details
```

### ✈️ رزرو پرواز
```bash
curl -X POST http://localhost:8000/api/flights/book \
  -H "Content-Type: application/json" \
  -d '{
    "offer_id": "off_123456",
    "passengers": [
      {
        "given_name": "John",
        "family_name": "Doe"
      }
    ],
    "payments": [
      {
        "type": "balance",
        "amount": "100.00",
        "currency": "USD"
      }
    ]
  }'
```

## ساختار پروژه

```
app/
├── Contracts/              # Interfaces
│   ├── TravelServiceInterface.php
│   └── FlightServiceInterface.php
├── DTOs/                   # Data Transfer Objects
│   ├── FlightSearchRequest.php
│   ├── FlightSearchResponse.php
│   └── ...
├── Exceptions/             # Custom Exceptions
│   └── TravelServiceException.php
├── Http/
│   ├── Controllers/Api/
│   │   ├── FlightController.php
│   │   └── TravelServiceController.php
│   ├── Requests/
│   │   └── FlightSearchRequest.php
│   └── Resources/
│       ├── FlightSearchResource.php
│       └── FlightOfferResource.php
├── Models/                 # Eloquent Models
│   ├── FlightSearch.php
│   ├── FlightOffer.php
│   └── FlightBooking.php
├── Services/               # Service Classes
│   ├── BaseService.php
│   ├── TravelServiceManager.php
│   └── Duffel/
│       └── DuffelService.php
└── Providers/
    └── TravelServiceProvider.php

config/
└── travel-services.php     # تنظیمات سرویس‌ها

routes/
└── api.php                 # API Routes
```

## اضافه کردن سرویس جدید

برای اضافه کردن سرویس جدید (مثل Amadeus):

### 1. ایجاد Service Class
```php
// app/Services/Amadeus/AmadeusService.php
class AmadeusService extends BaseService implements FlightServiceInterface
{
    protected string $providerName = 'amadeus';
    
    // پیاده‌سازی متدهای مورد نیاز
}
```

### 2. اضافه کردن به Manager
```php
// در TravelServiceManager
public function createAmadeusDriver(): AmadeusService
{
    $config = config('travel-services.providers.amadeus');
    return new AmadeusService($config);
}
```

### 3. تنظیمات
```php
// config/travel-services.php
'providers' => [
    'amadeus' => [
        'driver' => 'amadeus',
        'api_key' => env('AMADEUS_API_KEY'),
        'api_secret' => env('AMADEUS_API_SECRET'),
        // ...
    ],
]
```

## مونیتورینگ و لاگ‌ها

تمام عملیات در فایل‌های لاگ ثبت می‌شوند:

```bash
# مشاهده لاگ‌های سرویس سفر
tail -f storage/logs/laravel.log | grep "Travel service"
```

## تست‌ها

```bash
# اجرای تست‌ها
php artisan test

# تست‌های مربوط به سرویس‌های سفر
php artisan test --filter=Travel
```

## امنیت

- تمام API keys در متغیرهای محیط ذخیره می‌شوند
- Rate limiting برای جلوگیری از سوء استفاده
- Validation کامل ورودی‌ها
- Sanitization داده‌های حساس در لاگ‌ها

## پشتیبانی

برای سوالات و مشکلات:
- مستندات Duffel: https://duffel.com/docs
- Issues در GitHub
- تیم توسعه

---

**نکته**: این API در حال توسعه است و قابلیت‌های جدید به تدریج اضافه خواهند شد.
