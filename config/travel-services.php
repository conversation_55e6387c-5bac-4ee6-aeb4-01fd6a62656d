<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default Travel Service Provider
    |--------------------------------------------------------------------------
    |
    | This option controls the default travel service provider that will be used
    | by the travel service manager. You may set this to any of the providers
    | defined in the "providers" array below.
    |
    */

    'default' => env('TRAVEL_SERVICE_DEFAULT', 'duffel'),

    /*
    |--------------------------------------------------------------------------
    | Travel Service Providers
    |--------------------------------------------------------------------------
    |
    | Here you may configure the travel service providers for your application.
    | Each provider may have multiple configurations, allowing you to have
    | multiple providers of the same type with different settings.
    |
    */

    'providers' => [
        'duffel' => [
            'driver' => 'duffel',
            'api_key' => env('DUFFEL_API_KEY'),
            'base_url' => env('DUFFEL_BASE_URL', 'https://api.duffel.com'),
            'version' => env('DUFFEL_API_VERSION', 'v2'),
            'timeout' => env('DUFFEL_TIMEOUT', 30),
            'test_mode' => env('DUFFEL_TEST_MODE', true),
        ],

        // Future providers can be added here
        // 'amadeus' => [
        //     'driver' => 'amadeus',
        //     'api_key' => env('AMADEUS_API_KEY'),
        //     'api_secret' => env('AMADEUS_API_SECRET'),
        //     'base_url' => env('AMADEUS_BASE_URL', 'https://api.amadeus.com'),
        //     'timeout' => env('AMADEUS_TIMEOUT', 30),
        //     'test_mode' => env('AMADEUS_TEST_MODE', true),
        // ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Configure caching for travel service responses to improve performance
    | and reduce API calls to external providers.
    |
    */

    'cache' => [
        'enabled' => env('TRAVEL_CACHE_ENABLED', true),
        'ttl' => [
            'search_results' => env('TRAVEL_CACHE_SEARCH_TTL', 300), // 5 minutes
            'offer_details' => env('TRAVEL_CACHE_OFFER_TTL', 1800), // 30 minutes
            'airports' => env('TRAVEL_CACHE_AIRPORTS_TTL', 86400), // 24 hours
            'airlines' => env('TRAVEL_CACHE_AIRLINES_TTL', 86400), // 24 hours
        ],
        'prefix' => env('TRAVEL_CACHE_PREFIX', 'travel_'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Configure logging for travel service operations.
    |
    */

    'logging' => [
        'enabled' => env('TRAVEL_LOGGING_ENABLED', true),
        'channel' => env('TRAVEL_LOG_CHANNEL', 'stack'),
        'level' => env('TRAVEL_LOG_LEVEL', 'info'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configure rate limiting for API calls to prevent hitting provider limits.
    |
    */

    'rate_limiting' => [
        'enabled' => env('TRAVEL_RATE_LIMITING_ENABLED', true),
        'max_attempts' => env('TRAVEL_RATE_LIMIT_ATTEMPTS', 60),
        'decay_minutes' => env('TRAVEL_RATE_LIMIT_DECAY', 1),
    ],
];
