#!/bin/bash

# Travel Smart API Test Script
# This script tests the main API endpoints

BASE_URL="http://localhost:8000/api"

echo "🚀 Testing Travel Smart API..."
echo "================================"

# Test 1: Get available providers
echo "📋 Testing: Get available providers"
curl -s -X GET "$BASE_URL/travel/providers" | jq '.'
echo -e "\n"

# Test 2: Test Duffel provider
echo "🔧 Testing: Test Duffel provider"
curl -s -X GET "$BASE_URL/travel/providers/duffel/test" | jq '.'
echo -e "\n"

# Test 3: Get airports
echo "✈️ Testing: Get airports"
curl -s -X GET "$BASE_URL/flights/airports?query=London" | jq '.data[0:3]'
echo -e "\n"

# Test 4: Get airlines
echo "🛫 Testing: Get airlines"
curl -s -X GET "$BASE_URL/flights/airlines" | jq '.data[0:3]'
echo -e "\n"

# Test 5: Flight search (example)
echo "🔍 Testing: Flight search"
curl -s -X POST "$BASE_URL/flights/search" \
  -H "Content-Type: application/json" \
  -d '{
    "passengers": [
      {"type": "adult"}
    ],
    "slices": [
      {
        "origin": "LHR",
        "destination": "JFK",
        "departure_date": "2024-12-25"
      }
    ],
    "cabin_class": "economy"
  }' | jq '.'
echo -e "\n"

# Test 6: Validation test (should fail)
echo "❌ Testing: Validation (should fail)"
curl -s -X POST "$BASE_URL/flights/search" \
  -H "Content-Type: application/json" \
  -d '{}' | jq '.'
echo -e "\n"

echo "✅ API tests completed!"
echo "================================"
echo "💡 Tips:"
echo "- Make sure to set your DUFFEL_API_KEY in .env"
echo "- Run 'php artisan serve' to start the server"
echo "- Check logs with: tail -f storage/logs/laravel.log"
