<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * Flight Booking Model
 *
 * Represents a flight booking/order made through travel service providers
 */
class FlightBooking extends Model
{
    use HasFactory;

    protected $fillable = [
        'flight_offer_id',
        'user_id',
        'provider',
        'external_id',
        'booking_reference',
        'booking_data',
        'passengers',
        'payments',
        'services',
        'total_amount',
        'total_currency',
        'status',
        'booking_type',
        'booked_at',
        'cancelled_at',
        'cancellation_reason',
        'documents',
        'metadata',
    ];

    protected $casts = [
        'booking_data' => 'array',
        'passengers' => 'array',
        'payments' => 'array',
        'services' => 'array',
        'documents' => 'array',
        'metadata' => 'array',
        'booked_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'total_amount' => 'decimal:2',
    ];

    /**
     * Get the flight offer that was booked
     */
    public function flightOffer(): BelongsTo
    {
        return $this->belongsTo(FlightOffer::class);
    }

    /**
     * Get the user who made the booking
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Check if booking is confirmed
     */
    public function isConfirmed(): bool
    {
        return $this->status === 'confirmed';
    }

    /**
     * Check if booking is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if booking is pending
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if booking failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Mark booking as confirmed
     */
    public function markConfirmed(): void
    {
        $this->update([
            'status' => 'confirmed',
            'booked_at' => now(),
        ]);
    }

    /**
     * Mark booking as cancelled
     */
    public function markCancelled(string $reason = null): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
        ]);
    }

    /**
     * Mark booking as failed
     */
    public function markFailed(string $reason = null): void
    {
        $this->update([
            'status' => 'failed',
            'cancellation_reason' => $reason,
        ]);
    }

    /**
     * Get booking summary
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'external_id' => $this->external_id,
            'booking_reference' => $this->booking_reference,
            'provider' => $this->provider,
            'status' => $this->status,
            'booking_type' => $this->booking_type,
            'total_amount' => $this->total_amount,
            'total_currency' => $this->total_currency,
            'passengers_count' => count($this->passengers),
            'booked_at' => $this->booked_at?->format('c'),
            'cancelled_at' => $this->cancelled_at?->format('c'),
            'created_at' => $this->created_at->format('c'),
        ];
    }

    /**
     * Get passenger names
     */
    public function getPassengerNames(): array
    {
        $names = [];

        foreach ($this->passengers as $passenger) {
            $name = trim(($passenger['given_name'] ?? '') . ' ' . ($passenger['family_name'] ?? ''));
            if ($name) {
                $names[] = $name;
            }
        }

        return $names;
    }

    /**
     * Check if booking can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return in_array($this->status, ['confirmed', 'pending']) &&
               $this->cancelled_at === null;
    }
}
