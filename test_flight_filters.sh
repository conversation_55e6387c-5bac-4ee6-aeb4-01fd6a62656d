#!/bin/bash

# Flight Filter API Test Script
BASE_URL="http://localhost:8001/api/flights"

echo "🛫 Testing Flight Filter API"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Function to extract search ID from response
extract_search_id() {
    echo "$1" | grep -o '"id":"[^"]*"' | head -1 | cut -d'"' -f4
}

echo -e "${YELLOW}Step 1: Creating a flight search...${NC}"
SEARCH_RESPONSE=$(curl -s -X POST "$BASE_URL/search" \
  -H "Content-Type: application/json" \
  -d '{
    "passengers": [{"type": "adult"}],
    "slices": [{"origin": "LHR", "destination": "JFK", "departure_date": "2025-07-19"}],
    "cabin_class": "economy"
  }')

SEARCH_ID=$(extract_search_id "$SEARCH_RESPONSE")
print_result $? "Flight search created with ID: $SEARCH_ID"

if [ -z "$SEARCH_ID" ]; then
    echo -e "${RED}❌ Failed to create search. Exiting.${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}Step 2: Getting all offers (no filters)...${NC}"
ALL_OFFERS=$(curl -s "$BASE_URL/searches/$SEARCH_ID/offers")
TOTAL_OFFERS=$(echo "$ALL_OFFERS" | grep -o '"original_total":[0-9]*' | cut -d':' -f2)
print_result $? "Retrieved $TOTAL_OFFERS total offers"

echo ""
echo -e "${YELLOW}Step 3: Testing airline filter...${NC}"
AIRLINE_FILTER=$(curl -s -X POST "$BASE_URL/searches/$SEARCH_ID/offers/filter" \
  -H "Content-Type: application/json" \
  -d '{
    "airlines": ["BA", "LH"],
    "page": 1,
    "per_page": 10
  }')

FILTERED_COUNT=$(echo "$AIRLINE_FILTER" | grep -o '"filtered_total":[0-9]*' | cut -d':' -f2)
print_result $? "Airline filter applied - $FILTERED_COUNT offers found"

echo ""
echo -e "${YELLOW}Step 4: Testing price filter...${NC}"
PRICE_FILTER=$(curl -s -X POST "$BASE_URL/searches/$SEARCH_ID/offers/filter" \
  -H "Content-Type: application/json" \
  -d '{
    "max_price": 500,
    "currency": "USD",
    "sort_by": "price",
    "sort_order": "asc"
  }')

PRICE_FILTERED_COUNT=$(echo "$PRICE_FILTER" | grep -o '"filtered_total":[0-9]*' | cut -d':' -f2)
print_result $? "Price filter applied - $PRICE_FILTERED_COUNT offers under $500"

echo ""
echo -e "${YELLOW}Step 5: Testing direct flights filter...${NC}"
DIRECT_FILTER=$(curl -s -X POST "$BASE_URL/searches/$SEARCH_ID/offers/filter" \
  -H "Content-Type: application/json" \
  -d '{
    "direct_flights_only": true,
    "sort_by": "duration",
    "sort_order": "asc"
  }')

DIRECT_COUNT=$(echo "$DIRECT_FILTER" | grep -o '"filtered_total":[0-9]*' | cut -d':' -f2)
print_result $? "Direct flights filter applied - $DIRECT_COUNT direct flights found"

echo ""
echo -e "${YELLOW}Step 6: Testing time range filter...${NC}"
TIME_FILTER=$(curl -s -X POST "$BASE_URL/searches/$SEARCH_ID/offers/filter" \
  -H "Content-Type: application/json" \
  -d '{
    "departure_time_ranges": [
      {"start": "06:00", "end": "12:00"}
    ],
    "sort_by": "departure_time",
    "sort_order": "asc"
  }')

TIME_COUNT=$(echo "$TIME_FILTER" | grep -o '"filtered_total":[0-9]*' | cut -d':' -f2)
print_result $? "Morning departure filter applied - $TIME_COUNT morning flights found"

echo ""
echo -e "${YELLOW}Step 7: Testing combined filters...${NC}"
COMBINED_FILTER=$(curl -s -X POST "$BASE_URL/searches/$SEARCH_ID/offers/filter" \
  -H "Content-Type: application/json" \
  -d '{
    "airlines": ["BA"],
    "max_price": 800,
    "currency": "USD",
    "cabin_classes": ["economy"],
    "stops": [0, 1],
    "sort_by": "price",
    "sort_order": "asc",
    "page": 1,
    "per_page": 5
  }')

COMBINED_COUNT=$(echo "$COMBINED_FILTER" | grep -o '"filtered_total":[0-9]*' | cut -d':' -f2)
print_result $? "Combined filters applied - $COMBINED_COUNT offers match all criteria"

echo ""
echo -e "${YELLOW}Step 8: Testing filter options extraction...${NC}"
FILTER_OPTIONS=$(echo "$ALL_OFFERS" | grep -o '"filter_options":{[^}]*}')
if [ ! -z "$FILTER_OPTIONS" ]; then
    print_result 0 "Filter options extracted successfully"
else
    print_result 1 "Failed to extract filter options"
fi

echo ""
echo -e "${YELLOW}Step 9: Testing invalid filter parameters...${NC}"
INVALID_FILTER=$(curl -s -X POST "$BASE_URL/searches/$SEARCH_ID/offers/filter" \
  -H "Content-Type: application/json" \
  -d '{
    "sort_by": "invalid_sort",
    "max_price": 100,
    "min_price": 200
  }')

ERROR_RESPONSE=$(echo "$INVALID_FILTER" | grep -o '"success":false')
if [ ! -z "$ERROR_RESPONSE" ]; then
    print_result 0 "Invalid parameters correctly rejected"
else
    print_result 1 "Invalid parameters should have been rejected"
fi

echo ""
echo -e "${YELLOW}Step 10: Testing pagination...${NC}"
PAGINATED=$(curl -s "$BASE_URL/searches/$SEARCH_ID/offers?page=1&per_page=5")
PAGE_COUNT=$(echo "$PAGINATED" | grep -o '"per_page":[0-9]*' | cut -d':' -f2)
if [ "$PAGE_COUNT" = "5" ]; then
    print_result 0 "Pagination working correctly"
else
    print_result 1 "Pagination not working as expected"
fi

echo ""
echo "================================"
echo -e "${GREEN}🎉 Flight Filter API Tests Completed!${NC}"
echo ""
echo "Summary:"
echo "- Total offers found: $TOTAL_OFFERS"
echo "- Airline filtered: $FILTERED_COUNT"
echo "- Price filtered: $PRICE_FILTERED_COUNT"
echo "- Direct flights: $DIRECT_COUNT"
echo "- Morning flights: $TIME_COUNT"
echo "- Combined filters: $COMBINED_COUNT"
echo ""
echo "Test the API manually:"
echo "curl -X POST $BASE_URL/searches/$SEARCH_ID/offers/filter \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"airlines\": [\"BA\"], \"max_price\": 500}'"
